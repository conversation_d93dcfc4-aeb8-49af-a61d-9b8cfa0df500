# Portfolio Tracker Project Overview

## Purpose
Personal finance application with cross-platform support (web and mobile) for managing accounts, transactions, budgets, categories, and investments with real-time data synchronization.

## Key Features
- Account management with drag-and-drop reordering
- Transaction tracking with categories and templates
- Budget creation and monitoring
- Investment tracking (planned)
- CSV import/export functionality
- Analytics and reporting dashboard
- Multi-currency support
- Offline-first architecture with sync capabilities

## Architecture
- **Monorepo structure** using Turborepo
- **Cross-platform code sharing** in packages/shared
- **Platform-specific UI** in apps/web and apps/mobile
- **Database**: Supabase with generated TypeScript types
- **State Management**: Zustand + React Context
- **Authentication**: Supabase Auth with platform-specific implementations

## Current Development Phase
- **Primary Focus**: Backend + Web UI development
- **Mobile Development**: Postponed until web completion
- **Priority**: Feature completion and stability on web platform first