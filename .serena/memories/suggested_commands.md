# Suggested Commands

## Development Commands
- **Start Dev Server**: `npm run dev` (all packages) - Note: User runs this in background
- **Build All**: `npm run build`
- **Type Check**: `npm run type-check`
- **Lint All**: `npm run lint`
- **Test All**: `npm run test`

## Database Migration Commands
- **Apply Migrations**: `npm run migrate` (full-featured runner)
- **Supabase Migrations**: Use scripts/migrate-simple.js for Supabase compatibility

## Git Commands (Darwin/macOS)
- **Status**: `git status`
- **Add Files**: `git add .`
- **Commit**: `git commit -m "message"`
- **Push**: `git push origin main`

## System Commands (Darwin/macOS)
- **List Files**: `ls -la`
- **Find Files**: `find . -name "*.tsx"`
- **Search Content**: `grep -r "pattern" .`
- **Directory Navigation**: `cd`, `pwd`

## Important Notes
- **NEVER run dev server** - User runs it in background
- **Always use migration system** - Never apply manual SQL
- **Run lint/type-check** after completing tasks
- **Follow security-first approach** for all changes