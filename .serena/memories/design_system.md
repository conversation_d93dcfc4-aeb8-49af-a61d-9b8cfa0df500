# Design System & UI Components

## Color System
- **Primary**: Blue (#3B82F6) to <PERSON> (#8B5CF6) gradient
- **Semantic**: <PERSON> Green (#10B981), Error Red (#EF4444), Warning Orange (#F59E0B)
- **Surfaces**: Light mode (white/gray), Dark mode support
- **Always use semantic color variables** instead of hardcoded values

## Typography
- **Font**: Inter (primary), Roboto Mono (code)
- **Scale**: xs(12px) to 3xl(30px)
- **Weights**: normal(400), medium(500), semibold(600), bold(700)

## Key UI Components
- **PageLayout**: Consistent page structure with title, description, actions
- **ContentCard**: Unified card styling with hover states
- **LoadingState**: Standardized loading indicators
- **EmptyState**: Consistent empty states with CTAs
- **TabNavigation**: Uniform tab interface with counts
- **MetricCard**: Dashboard metric display cards
- **CategoryIcon**: Pattern for all icon displays
- **DraggableAccountCard**: Drag and drop functionality with @dnd-kit

## Currency Requirement
**CRITICAL**: Always use user's selected currency from currency store/context. Never hardcode USD or any specific currency.

## Icon Library
- **Library**: Lucide React
- **Default Size**: 20px (w-5 h-5)
- **Stroke Width**: 2px

## Layout Patterns
- **Spacing Scale**: Consistent 4px, 8px, 12px, 16px, 20px, 24px, 32px, 40px
- **Border Radius**: 2px, 4px, 6px, 8px, 12px, 16px, 24px
- **Shadows**: Proper opacity values for depth