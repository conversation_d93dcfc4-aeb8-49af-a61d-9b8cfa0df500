# Coding Standards & Conventions

## Naming Conventions
- **Components**: <PERSON><PERSON>ase (ExpenseCard.tsx)
- **Utilities**: camelCase (formatCurrency.ts)
- **Types/Interfaces**: PascalCase with 'I' or 'T' prefix (IExpense, TCategory)
- **Constants**: UPPER_SNAKE_CASE (MAX_BUDGET_AMOUNT)
- **Database tables**: snake_case (expense_categories)
- **API endpoints**: kebab-case (/api/get-expenses)

## Code Style
- **TypeScript**: Strict mode enabled
- **Comments**: DO NOT ADD unless explicitly requested
- **Error Handling**: Proper error boundaries and fallbacks
- **Loading States**: Implement for all async operations
- **Validation**: Both client and server-side validation

## Security Requirements
- Never store sensitive data in plain text
- Use prepared statements for database queries
- Validate all user inputs
- Implement rate limiting on API endpoints
- Use HTTPS for all network requests
- Encrypt sensitive data with AES-256

## Component Architecture
- **Design Pattern**: Atomic design principles
- **Reusable Components**: PageLayout, ContentCard, LoadingState, EmptyState, TabNavigation, MetricCard
- **Platform Separation**: UI components are platform-specific
- **Shared Logic**: Business logic in packages/shared