# Tech Stack

## Frontend
- **Web**: Next.js 14 with App Router, Tailwind CSS
- **Mobile**: React Native + Expo (SDK 50), NativeWind
- **Charts**: Recharts (web), Victory Native (mobile)
- **Icons**: Lucide React
- **Forms**: React Hook Form + Zod validation

## Backend & Infrastructure
- **Database**: Supabase (PostgreSQL + Auth + Realtime)
- **Authentication**: Supabase Auth
- **File Storage**: Supabase Storage

## Development Tools
- **TypeScript**: Strict mode enabled
- **Build System**: Turborepo
- **Package Manager**: npm
- **State Management**: Zustand for global state
- **Drag & Drop**: @dnd-kit (React 19 compatible)

## Key Libraries
- **UI Components**: Custom design system with reusable components
- **Validation**: Zod schemas
- **Currency**: User-selected currency system
- **Migrations**: Custom migration system for database schema changes