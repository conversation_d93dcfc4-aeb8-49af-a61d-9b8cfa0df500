# Task Completion Checklist

## After Completing Any Development Task

### 1. Code Quality Checks
- **Type Check**: Run `npm run type-check` to ensure TypeScript compliance
- **Linting**: Run `npm run lint` to check code style
- **Fix Issues**: Address any TypeScript errors or lint warnings

### 2. Testing (if applicable)
- **Run Tests**: Execute `npm run test` if tests exist
- **Manual Testing**: Test the feature in browser at localhost:3000
- **Cross-platform**: Ensure shared code works for both web and mobile

### 3. Database Changes
- **Migration Required**: If schema changes, create migration file in /migrations/
- **Apply Migration**: Run migration system before deployment
- **Verify Changes**: Ensure database schema matches code expectations

### 4. Documentation Updates
- **Update CLAUDE.local.md**: Add new files to folder structure
- **API Changes**: Document any new endpoints or data structures
- **Component Changes**: Update component documentation if creating new patterns

### 5. Security Review
- **Input Validation**: Ensure all user inputs are validated
- **Sensitive Data**: Check no secrets are exposed or logged
- **Authentication**: Verify proper auth checks on protected routes

### 6. Performance Check
- **Bundle Size**: Monitor for significant increases
- **Loading States**: Ensure proper loading indicators
- **Error Handling**: Verify graceful error handling

## Critical Requirements
- **NEVER commit without explicit user request**
- **Always use TypeScript strict mode**
- **Follow security-first principles**
- **Update folder structure documentation immediately**