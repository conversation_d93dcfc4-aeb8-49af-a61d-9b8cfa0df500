// Test for HDFC PDF Parser - Fixed Version
console.log('=== HDFC PDF Parser Test - Fixed Version ===\\n');\n\n// Test the specific concatenated format that HDFC PDFs produce\nconst testLines = [\n  \"01/04/25NEFT DR-ICIC0001550-ARAVINTH E-NETBANK,HDFCN5202504015026481101/04/2578,100.00188,702.88\",\n  \"02/04/25UPI-ARAVINTH E-ARAVINTH-ELANGOVAN@DBS-DB 000010245271068102/04/2511,000.00177,702.88\",\n  \"02/04/25ACH C- MUTHOOT FINANCE LIMI-7GV219790000000667432393502/04/2513.00177,715.88\",\n  \"03/04/25UPI-INDIAN CLEARING CORP-ICCLZERODHA@YES 000050936110433303/04/2510,000.00167,793.88\"\n];\n\n// Expected results based on the actual PDF\nconst expectedResults = [\n  { amount: 78100.00, balance: 188702.88, type: 'debit', description: 'NEFT DR-ICIC0001550' },\n  { amount: 11000.00, balance: 177702.88, type: 'debit', description: 'UPI-ARAVINTH' },\n  { amount: 13.00, balance: 177715.88, type: 'credit', description: 'ACH C- MUTHOOT FINANCE' },\n  { amount: 10000.00, balance: 167793.88, type: 'debit', description: 'UPI-INDIAN CLEARING' }\n];\n\nconsole.log('Expected Results:');\nexpectedResults.forEach((expected, index) => {\n  console.log(`${index + 1}. ${expected.description} - ${expected.type} - ₹${expected.amount.toLocaleString()} - Balance: ₹${expected.balance.toLocaleString()}`);\n});\n\n// Test the concatenated amount parsing logic\nfunction testParseHDFCConcatenatedAmounts(concatenatedStr) {\n  try {\n    console.log('  📊 Parsing concatenated amounts:', concatenatedStr);\n    \n    // Step 1: Find the closing balance\n    const balanceMatch = concatenatedStr.match(/(\\d{2,3},\\d{3}\\.\\d{2})$/);\n    \n    let closingBalance;\n    let remainingAfterBalance;\n    \n    if (balanceMatch) {\n      // Case: balance has comma (e.g., 177,715.88)\n      closingBalance = parseFloat(balanceMatch[1].replace(/,/g, ''));\n      remainingAfterBalance = concatenatedStr.substring(0, concatenatedStr.lastIndexOf(balanceMatch[1]));\n      console.log('  ✅ Found balance with comma:', balanceMatch[1], '->', closingBalance);\n    } else {\n      // Case: balance might be concatenated without comma (e.g., 188702.88)\n      const concatenatedBalanceMatch = concatenatedStr.match(/(\\d{6}\\.\\d{2})$/);\n      if (concatenatedBalanceMatch) {\n        const balanceStr = concatenatedBalanceMatch[1];\n        // Insert comma: 188702.88 → 188,702.88\n        const formattedBalance = balanceStr.replace(/(\\d{3})(\\d{3}\\.\\d{2})$/, '$1,$2');\n        closingBalance = parseFloat(formattedBalance.replace(/,/g, ''));\n        remainingAfterBalance = concatenatedStr.substring(0, concatenatedStr.lastIndexOf(balanceStr));\n        console.log('  ✅ Found concatenated balance:', balanceStr, '→', formattedBalance, '->', closingBalance);\n      } else {\n        console.log('  ❌ Could not find closing balance pattern');\n        return null;\n      }\n    }\n    \n    console.log('  📝 Remaining after removing balance:', remainingAfterBalance);\n    \n    // Step 2: Parse transaction amount\n    let transactionAmount;\n    \n    if (remainingAfterBalance.includes(',')) {\n      // Case: amount has comma (e.g., \"78,100.00\" or \"11,000.00\")\n      const amountWithCommaMatch = remainingAfterBalance.match(/(\\d{1,2},\\d{3}\\.\\d{2})$/);\n      if (amountWithCommaMatch) {\n        transactionAmount = parseFloat(amountWithCommaMatch[1].replace(/,/g, ''));\n        console.log('  ✅ Found amount with comma:', amountWithCommaMatch[1], '->', transactionAmount);\n      } else {\n        console.log('  ❌ Could not parse amount with comma');\n        return null;\n      }\n    } else {\n      // Case: amount without comma (e.g., \"13.00\")\n      const amountMatch = remainingAfterBalance.match(/(\\d{1,5}\\.\\d{2})$/);\n      if (amountMatch) {\n        transactionAmount = parseFloat(amountMatch[1]);\n        console.log('  ✅ Found amount without comma:', amountMatch[1], '->', transactionAmount);\n      } else {\n        console.log('  ❌ Could not parse amount without comma');\n        return null;\n      }\n    }\n    \n    return { transactionAmount, closingBalance };\n  } catch (error) {\n    console.error('  ❌ Error parsing concatenated amounts:', error);\n    return null;\n  }\n}\n\n// Test the main parsing logic\nfunction testParseHDFCTransactionLine(fullLine, transactionDate) {\n  console.log('\\n🔍 Testing HDFC Transaction Line:');\n  console.log('📄 Input:', fullLine);\n  \n  try {\n    // Remove the initial transaction date\n    let remaining = fullLine.replace(/^\\d{2}\\/\\d{2}\\/\\d{2,4}/, '').trim();\n    console.log('📝 After removing date:', remaining);\n    \n    // Find the last occurrence of \"25\" which marks the start of concatenated amounts\n    const year25Index = remaining.lastIndexOf('25');\n    \n    if (year25Index === -1) {\n      console.log('❌ Could not find year marker \"25\"');\n      return null;\n    }\n    \n    console.log('📍 Found \"25\" at index:', year25Index);\n    \n    // Extract the concatenated amounts after \"25\"\n    const concatenatedAmounts = remaining.substring(year25Index + 2);\n    console.log('🔗 Concatenated amounts:', concatenatedAmounts);\n    \n    // Test our concatenated amount parsing\n    const amounts = testParseHDFCConcatenatedAmounts(concatenatedAmounts);\n    if (!amounts) {\n      console.log('❌ Failed to parse concatenated amounts');\n      return null;\n    }\n    \n    console.log('✅ Successfully parsed amounts:', amounts);\n    return amounts;\n  } catch (error) {\n    console.error('❌ Error parsing transaction line:', error);\n    return null;\n  }\n}\n\n// Run the tests\nconsole.log('\\n=== Running Parser Tests ===');\n\ntestLines.forEach((line, index) => {\n  const dateMatch = line.match(/^(\\d{2}\\/\\d{2}\\/\\d{2,4})/);\n  if (dateMatch) {\n    console.log(`\\n=== 🧪 Test ${index + 1}: ${expectedResults[index].description} ===`);\n    const result = testParseHDFCTransactionLine(line, dateMatch[1]);\n    \n    if (result) {\n      const expected = expectedResults[index];\n      console.log('\\n📊 Results Comparison:');\n      \n      const amountCorrect = Math.abs(result.transactionAmount - expected.amount) < 0.01;\n      const balanceCorrect = Math.abs(result.closingBalance - expected.balance) < 0.01;\n      \n      console.log(`💰 Amount: Expected ${expected.amount.toLocaleString()} vs Parsed ${result.transactionAmount.toLocaleString()} ${amountCorrect ? '✅' : '❌'}`);\n      console.log(`💳 Balance: Expected ${expected.balance.toLocaleString()} vs Parsed ${result.closingBalance.toLocaleString()} ${balanceCorrect ? '✅' : '❌'}`);\n      \n      if (amountCorrect && balanceCorrect) {\n        console.log('🎉 SUCCESS: Transaction parsed correctly!');\n      } else {\n        console.log('⚠️  NEEDS ADJUSTMENT: Some amounts are incorrect');\n      }\n    } else {\n      console.log('❌ FAILED: Could not parse this transaction');\n    }\n  }\n});\n\nconsole.log('\\n=== Test Summary ===');\nconsole.log('If you see 🎉 SUCCESS messages, the new parser is working correctly!');\nconsole.log('If you see ❌ FAILED or ⚠️  NEEDS ADJUSTMENT, there may still be issues to fix.');\nconsole.log('\\nThe new parser specifically handles HDFC\\'s concatenated amount format.');\n