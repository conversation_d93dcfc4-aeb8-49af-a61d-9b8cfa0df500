# PDF Bank Statement Import Feature

This feature allows users to upload HDFC Bank statement PDFs and extract transaction data automatically. The extracted data can be downloaded in text, CSV, and Excel formats.

## Installation

1. **Install dependencies** for the web app:
   ```bash
   cd apps/web
   npm install
   ```

2. **Install dependencies** for the shared package:
   ```bash
   cd packages/shared
   npm install
   ```

3. **Install root dependencies** (if running from root):
   ```bash
   npm install
   ```

## Features

✅ **PDF Upload**: Drag & drop or browse to upload HDFC Bank statement PDFs  
✅ **Transaction Extraction**: Automatically parse transaction data from PDF  
✅ **Multiple Export Formats**: Download as Text, CSV, or Excel  
✅ **Progress Tracking**: Visual progress indicator during PDF processing  
✅ **Error Handling**: User-friendly error messages with retry options  
✅ **Statement Summary**: Display account info, period, and transaction counts  

## How to Use

1. **Navigate to Transactions Page**: Go to `/transactions` in your app
2. **Click "Import PDF"**: Look for the Upload icon button in the page actions
3. **Upload PDF**: Drag & drop or browse to select your HDFC Bank statement PDF
4. **View Results**: See extracted transaction summary and download options
5. **Download Data**: Choose from Text, CSV, or Excel format downloads

## File Structure

```
packages/shared/src/lib/
├── pdf-parser.ts           # PDF parsing logic for HDFC Bank statements
└── statement-export.ts     # Export utilities for text, CSV, Excel

apps/web/src/
├── app/api/pdf/parse/route.ts    # API route for server-side PDF processing
└── components/PDFUpload.tsx      # React component for PDF upload UI
```

## Supported Banks

Currently supports:
- **HDFC Bank** statement format

The parser can be extended to support other bank formats by adding new parsing methods to the `BankStatementParser` class.

## API Endpoints

### POST `/api/pdf/parse`
Processes uploaded PDF files and extracts transaction data.

**Request**: FormData with 'file' field containing PDF
**Response**: JSON with extracted bank statement data

```typescript
{
  success: boolean;
  data: {
    accountHolder: string;
    accountNumber: string;
    statementPeriod: { from: string; to: string };
    openingBalance: number;
    closingBalance: number;
    transactions: ParsedTransaction[];
    summary: {
      totalDebits: number;
      totalCredits: number;
      debitCount: number;
      creditCount: number;
    };
  };
}
```

## Configuration

- **File Size Limit**: 10MB maximum
- **File Type**: PDF only
- **Processing**: Server-side using pdf-parse library

## Error Handling

The system handles common errors:
- Invalid file type (non-PDF)
- File size too large (>10MB)
- PDF parsing failures
- Network/server errors

## Security Considerations

- PDF processing is done server-side
- No PDF content is stored permanently
- File uploads are validated for type and size
- Error messages don't expose sensitive information

## Future Enhancements

- [ ] Support for additional bank formats (SBI, ICICI, etc.)
- [ ] Automatic transaction import to database
- [ ] OCR support for scanned PDFs
- [ ] Batch processing for multiple statements
- [ ] Transaction categorization suggestions
