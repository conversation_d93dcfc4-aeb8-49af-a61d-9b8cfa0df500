{"permissions": {"allow": ["Bash(npx create-turbo:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(npx create-expo-app:*)", "<PERSON><PERSON>(mv:*)", "<PERSON><PERSON>(true)", "<PERSON><PERSON>(rmdir:*)", "Bash(npx create-next-app:*)", "Bash(npm install)", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(pkill:*)", "Bash(npm run build:*)", "mcp__supabase-mcp-server__list_projects", "mcp__supabase-mcp-server__get_project", "mcp__supabase-mcp-server__get_project_url", "mcp__supabase-mcp-server__get_anon_key", "mcp__supabase-mcp-server__list_tables", "mcp__supabase-mcp-server__apply_migration", "mcp__supabase-mcp-server__generate_typescript_types", "mcp__supabase-mcp-server__execute_sql", "Bash(npm install:*)", "Bash(rm:*)", "Bash(npm run type-check:*)", "Bash(find:*)", "Bash(npm run dev:*)", "Bash(npx tsc:*)", "Bash(npm run lint)", "Bash(ls:*)", "Bash(timeout 10s npm run dev)", "Bash(npx next dev --port 3001)", "Bash(npx turbo build:*)", "Bash(rg:*)", "Bash(npm run typecheck:*)", "Bash(grep:*)", "mcp__supabase-mcp-server__list_migrations", "Bash(cp:*)", "mcp__ide__getDiagnostics", "Bash(npm ls:*)", "mcp__supabase-mcp-server__get_logs", "Bash(node:*)", "Bash(NEXT_PUBLIC_SUPABASE_URL=https://iconspmonvnujpnfmyqx.supabase.co NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imljb25zcG1vbnZudWpwbmZteXF4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAyMzQwMDEsImV4cCI6MjA2NTgxMDAwMX0.xKtYUC7fBGZXlrxrI_DJxMvzBL1th7h60H2Qn-x6-So node test-category-constraint.js)", "<PERSON><PERSON>(chmod:*)", "Bash(./fix-navbar.sh:*)", "Bash(npx supabase gen types typescript:*)", "Bash(npx supabase:*)", "Bash(npm run:*)", "mcp__puppeteer__puppeteer_navigate", "mcp__puppeteer__puppeteer_screenshot", "mcp__memory__create_entities", "mcp__memory__create_relations", "mcp__puppeteer__puppeteer_evaluate", "mcp__puppeteer__puppeteer_click", "mcp__playwright__playwright_navigate", "<PERSON><PERSON>(npx playwright:*)", "mcp__playwright__playwright_evaluate", "mcp__memory__read_graph", "Bash(git submodule:*)", "mcp__serena__initial_instructions", "mcp__serena__check_onboarding_performed", "mcp__serena__onboarding", "mcp__serena__write_memory", "mcp__memory__add_observations", "mcp__sequential-thinking__sequentialthinking", "mcp__graphiti-memory__add_memory", "mcp__graphiti-memory__get_episodes", "mcp__graphiti-memory__search_memory_nodes", "mcp__neo4j-memory__get_neo4j_schema", "mcp__memory__search_nodes", "Bash(echo $GVM_ROOT)", "<PERSON><PERSON>(sed:*)", "<PERSON><PERSON>(source:*)", "Bash(sudo rm:*)"], "deny": []}}