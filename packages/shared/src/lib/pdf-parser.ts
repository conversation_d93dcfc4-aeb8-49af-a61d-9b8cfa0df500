import type { default as PDFParseFunction } from 'pdf-parse';

export interface ParsedTransaction {
  date: string;
  narration: string;
  refNo: string;
  valueDate: string;
  withdrawalAmount: number | null;
  depositAmount: number | null;
  closingBalance: number;
  type: 'credit' | 'debit';
}

export interface BankStatementData {
  accountHolder: string;
  accountNumber: string;
  statementPeriod: {
    from: string;
    to: string;
  };
  openingBalance: number;
  closingBalance: number;
  transactions: ParsedTransaction[];
  summary: {
    totalDebits: number;
    totalCredits: number;
    debitCount: number;
    creditCount: number;
  };
}

export class BankStatementParser {
  static async parsePDF(buffer: Buffer): Promise<BankStatementData> {
    try {
      // Dynamic import to avoid loading pdf-parse at module level
      const pdf = (await import('pdf-parse')).default as typeof PDFParseFunction;
      
      const data = await pdf(buffer);
      const text = data.text;
      
      return this.parseHDFCStatement(text);
    } catch (error) {
      throw new Error(`Failed to parse PDF: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private static parseHDFCStatement(text: string): BankStatementData {
    const lines = text.split('\n').map(line => line.trim()).filter(line => line.length > 0);
    
    console.log('PDF Text Length:', text.length);
    console.log('Total Lines:', lines.length);
    console.log('First 10 lines:', lines.slice(0, 10));
    
    // Extract account holder
    const accountHolder = this.extractAccountHolder(lines);
    console.log('Account Holder:', accountHolder);
    
    // Extract account number
    const accountNumber = this.extractAccountNumber(lines);
    console.log('Account Number:', accountNumber);
    
    // Extract statement period
    const statementPeriod = this.extractStatementPeriod(lines);
    console.log('Statement Period:', statementPeriod);
    
    // Extract opening and closing balance
    const { openingBalance, closingBalance } = this.extractBalances(lines);
    console.log('Balances:', { openingBalance, closingBalance });
    
    // Extract transactions
    const transactions = this.extractTransactions(lines);
    console.log('Extracted Transactions:', transactions.length);
    
    // Calculate summary
    const summary = this.calculateSummary(transactions);
    
    return {
      accountHolder,
      accountNumber,
      statementPeriod,
      openingBalance,
      closingBalance,
      transactions,
      summary
    };
  }

  private static extractAccountHolder(lines: string[]): string {
    for (let i = 0; i < lines.length; i++) {
      if (lines[i].startsWith('MR ') || lines[i].startsWith('MS ') || lines[i].startsWith('DR ')) {
        return lines[i];
      }
    }
    return 'Unknown Account Holder';
  }

  private static extractAccountNumber(lines: string[]): string {
    for (let i = 0; i < lines.length; i++) {
      if (lines[i].includes('Account No')) {
        const match = lines[i].match(/Account No\s*:\s*(\d+)/);
        if (match) {
          return match[1];
        }
      }
    }
    return 'Unknown Account Number';
  }

  private static extractStatementPeriod(lines: string[]): { from: string; to: string } {
    for (let i = 0; i < lines.length; i++) {
      if (lines[i].includes('Statement From')) {
        const match = lines[i].match(/Statement From\s*:\s*(\d{2}\/\d{2}\/\d{4})\s+To:\s*(\d{2}\/\d{2}\/\d{4})/);
        if (match) {
          return {
            from: match[1],
            to: match[2]
          };
        }
      }
    }
    return { from: '', to: '' };
  }

  private static extractBalances(lines: string[]): { openingBalance: number; closingBalance: number } {
    let openingBalance = 0;
    let closingBalance = 0;

    // Look for the summary line at the end of HDFC statements
    // It usually appears near the end with a pattern like: opening_balance count count total_debits total_credits closing_balance
    for (let i = lines.length - 20; i < lines.length; i++) {
      if (i >= 0 && lines[i]) {
        // Look for the summary pattern: opening_balance debits credits closing_balance
        const summaryMatch = lines[i].match(/^(\d{1,3}(?:,\d{3})*\.\d{2})\s+\d+\s+\d+\s+(\d{1,3}(?:,\d{3})*\.\d{2})\s+(\d{1,3}(?:,\d{3})*\.\d{2})\s+(\d{1,3}(?:,\d{3})*\.\d{2})$/);
        if (summaryMatch) {
          openingBalance = parseFloat(summaryMatch[1].replace(/,/g, ''));
          closingBalance = parseFloat(summaryMatch[4].replace(/,/g, ''));
          console.log('Found balance summary:', { openingBalance, closingBalance });
          return { openingBalance, closingBalance };
        }
      }
    }

    // If not found in summary, extract from transactions using same logic as transaction parser
    const firstTransactionBalance = this.getFirstTransactionBalance(lines);
    const lastTransactionBalance = this.getLastTransactionBalance(lines);
    
    if (firstTransactionBalance !== null) {
      // For opening balance, we need to calculate backwards from first transaction
      const firstTxnAmount = this.getFirstTransactionAmount(lines);
      const firstTxnIsCredit = this.isFirstTransactionCredit(lines);
      
      if (firstTxnAmount !== null) {
        if (firstTxnIsCredit) {
          openingBalance = firstTransactionBalance - firstTxnAmount;
        } else {
          openingBalance = firstTransactionBalance + firstTxnAmount;
        }
      } else {
        openingBalance = firstTransactionBalance;
      }
    }
    
    if (lastTransactionBalance !== null) {
      closingBalance = lastTransactionBalance;
    }

    console.log('Extracted balances from transactions:', { openingBalance, closingBalance });
    return { openingBalance, closingBalance };
  }

  private static getFirstTransactionBalance(lines: string[]): number | null {
    for (let i = 0; i < lines.length; i++) {
      const dateMatch = lines[i].match(/^(\d{2}\/\d{2}\/\d{2,4})/);
      if (dateMatch && !lines[i].includes('Statement From')) {
        // Build full transaction line
        let fullLine = lines[i];
        for (let j = i + 1; j < Math.min(i + 5, lines.length); j++) {
          if (lines[j] && !lines[j].match(/^\d{2}\/\d{2}\/\d{2}/) && !lines[j].includes('Statement of account')) {
            fullLine += ' ' + lines[j];
          } else {
            break;
          }
        }
        
        // Extract closing balance (rightmost amount)
        const balanceMatch = fullLine.match(/(\d{1,3}(?:,\d{3})*\.\d{2})$/);        
        if (balanceMatch) {
          return parseFloat(balanceMatch[1].replace(/,/g, ''));
        }
      }
    }
    return null;
  }

  private static getLastTransactionBalance(lines: string[]): number | null {
    for (let i = lines.length - 1; i >= 0; i--) {
      const dateMatch = lines[i].match(/^(\d{2}\/\d{2}\/\d{2,4})/);
      if (dateMatch && !lines[i].includes('Statement From')) {
        // Build full transaction line
        let fullLine = lines[i];
        for (let j = i + 1; j < Math.min(i + 5, lines.length); j++) {
          if (lines[j] && !lines[j].match(/^\d{2}\/\d{2}\/\d{2}/) && !lines[j].includes('Statement of account')) {
            fullLine += ' ' + lines[j];
          } else {
            break;
          }
        }
        
        // Extract closing balance (rightmost amount)
        const balanceMatch = fullLine.match(/(\d{1,3}(?:,\d{3})*\.\d{2})$/);        
        if (balanceMatch) {
          return parseFloat(balanceMatch[1].replace(/,/g, ''));
        }
      }
    }
    return null;
  }

  private static getFirstTransactionAmount(lines: string[]): number | null {
    for (let i = 0; i < lines.length; i++) {
      const dateMatch = lines[i].match(/^(\d{2}\/\d{2}\/(\d{2}))/);
      if (dateMatch && !lines[i].includes('Statement From')) {
        const transactionDate = dateMatch[1];
        const yearMarker = dateMatch[2]; // Extract year from date
        
        // Build full transaction line
        let fullLine = lines[i];
        for (let j = i + 1; j < Math.min(i + 5, lines.length); j++) {
          if (lines[j] && !lines[j].match(/^\d{2}\/\d{2}\/\d{2}/) && !lines[j].includes('Statement of account')) {
            fullLine += ' ' + lines[j];
          } else {
            break;
          }
        }
        
        // Use the year-agnostic parsing approach
        let remaining = fullLine.replace(/^\d{2}\/\d{2}\/\d{2,4}/, '').trim();
        const yearMarkerIndex = remaining.lastIndexOf(yearMarker);
        
        if (yearMarkerIndex !== -1) {
          const concatenatedAmounts = remaining.substring(yearMarkerIndex + yearMarker.length);
          const amounts = this.parseHDFCConcatenatedAmounts(concatenatedAmounts);
          if (amounts) {
            return amounts.transactionAmount;
          }
        }
        
        // Fallback: try the old method
        const closingBalanceMatch = remaining.match(/(\d{1,3}(?:,\d{3})*\.\d{2})$/);
        if (closingBalanceMatch) {
          remaining = remaining.substring(0, remaining.lastIndexOf(closingBalanceMatch[1])).trim();
          const transactionAmountMatch = remaining.match(/(\d{1,3}(?:,\d{3})*\.\d{2})$/);
          if (transactionAmountMatch) {
            return parseFloat(transactionAmountMatch[1].replace(/,/g, ''));
          }
        }
      }
    }
    return null;
  }

  private static isFirstTransactionCredit(lines: string[]): boolean {
    for (let i = 0; i < lines.length; i++) {
      const dateMatch = lines[i].match(/^(\d{2}\/\d{2}\/\d{2,4})/);
      if (dateMatch && !lines[i].includes('Statement From')) {
        // Build full transaction line
        let fullLine = lines[i];
        for (let j = i + 1; j < Math.min(i + 5, lines.length); j++) {
          if (lines[j] && !lines[j].match(/^\d{2}\/\d{2}\/\d{2}/) && !lines[j].includes('Statement of account')) {
            fullLine += ' ' + lines[j];
          } else {
            break;
          }
        }
        
        // Check if it's a credit transaction
        return fullLine.toLowerCase().includes('ach c-') || 
               fullLine.toLowerCase().includes('neft cr') ||
               fullLine.toLowerCase().includes('salary');
      }
    }
    return false;
  }

  private static extractTransactions(lines: string[]): ParsedTransaction[] {
    const transactions: ParsedTransaction[] = [];
    console.log('Looking for transactions in', lines.length, 'lines');
    
    // Debug: Show all lines that contain amounts to help identify pattern
    console.log('Lines with amounts:');
    lines.forEach((line, index) => {
      if (/\d{1,3}(?:,\d{3})*(?:\.\d{1,2})?/.test(line)) {
        console.log(`${index}: ${line}`);
      }
    });
    
    for (let i = 0; i < lines.length; i++) {
      // Enhanced header/footer content detection
      if (lines[i].includes('Statement of account') || 
          lines[i].includes('Page No') ||
          lines[i].includes('HDFC Bank') ||
          lines[i].includes('HDFC BANK LIMITED') ||
          lines[i].includes('We understand your world') ||
          lines[i].includes('Phone no') ||
          lines[i].includes('Email') ||
          lines[i].includes('Address') ||
          lines[i].includes('NAMAKKAL') ||
          lines[i].includes('TAMIL NADU') ||
          lines[i].includes('JOINT HOLDERS') ||
          lines[i].includes('Nomination') ||
          lines[i].includes('Contents of this statement') ||
          lines[i].includes('Registered Office') ||
          lines[i].includes('Branch Code') ||
          lines[i].includes('Account Type') ||
          lines[i].includes('Account No') ||
          lines[i].includes('Cust ID') ||
          lines[i].includes('MICR') ||
          lines[i].includes('IFSC') ||
          lines[i].includes('GSTN') ||
          lines[i].includes('Currency') ||
          lines[i].includes('*Closing balance includes') ||
          lines[i].includes('Generated')) {
        continue;
      }
      
      // Look for transaction date pattern (DD/MM/YY format for HDFC)
      // Enhanced pattern to catch variations
      const dateMatch = lines[i].match(/^(\d{2}\/\d{2}\/\d{2,4})(.*)/);
      
      if (dateMatch) {
        console.log('Found potential transaction:', lines[i]);
        const date = dateMatch[1];
        const remainingText = dateMatch[2];
        
        // Skip if this looks like a header line with dates (Statement From/To)
        if (remainingText.includes('Statement From') || 
            remainingText.includes('To :') ||
            remainingText.includes('To :')||            // Handle possible spacing variations
            lines[i].includes('Statement From')) {
          console.log('Skipping header line with date:', lines[i]);
          continue;
        }
        
        // Skip lines that are just dates without transaction data
        if (remainingText.trim().length < 5) {
          console.log('Skipping line with insufficient transaction data:', lines[i]);
          continue;
        }
        
        // Try to parse the transaction line
        const transaction = this.parseTransactionLine(date, remainingText, lines, i);
        if (transaction) {
          console.log('Successfully parsed transaction:', transaction);
          transactions.push(transaction);
        } else {
          console.log('Failed to parse transaction line, adding to debug log for manual review');
          console.log('Failed line details:', {
            index: i,
            line: lines[i],
            date: date,
            remainingText: remainingText,
            nextFewLines: lines.slice(i + 1, i + 4)
          });
        }
      }
    }
    
    console.log('Total transactions found:', transactions.length);
    console.log('Transaction dates found:', transactions.map(t => t.date));
    return transactions;
  }

  private static parseTransactionLine(
    date: string, 
    remainingText: string, 
    lines: string[], 
    currentIndex: number
  ): ParsedTransaction | null {
    try {
      // The HDFC PDF format puts everything in one long line like:
      // "01/04/25NEFT DR-ICIC0001550-ARAVINTH E-NETBANK,HDFCN5202504015026481101/04/2578,100.00188,702.88"
      // We need to parse this more intelligently
      
      let fullLine = date + remainingText;
      
      // Enhanced multi-line transaction handling
      let nextLineIndex = currentIndex + 1;
      let addedLines = 0;
      while (nextLineIndex < lines.length && addedLines < 5) {
        const nextLine = lines[nextLineIndex].trim();
        
        // Stop if next line starts with a date (new transaction)
        if (/^\d{2}\/\d{2}\/\d{2}/.test(nextLine)) {
          break;
        }
        
        // Enhanced header/footer detection
        if (nextLine.includes('Statement of account') || 
            nextLine.includes('Page No') ||
            nextLine.includes('HDFC Bank') ||
            nextLine.includes('HDFC BANK LIMITED') ||
            nextLine.includes('We understand your world') ||
            nextLine.includes('Phone no') ||
            nextLine.includes('Email') ||
            nextLine.includes('Address') ||
            nextLine.includes('NAMAKKAL') ||
            nextLine.includes('TAMIL NADU') ||
            nextLine.includes('JOINT HOLDERS') ||
            nextLine.includes('Nomination') ||
            nextLine.includes('Contents of this statement') ||
            nextLine.includes('Registered Office') ||
            nextLine.includes('Generated') ||
            nextLine.includes('Branch Code') ||
            nextLine.includes('Account Type') ||
            nextLine.includes('MICR') ||
            nextLine.includes('IFSC') ||
            nextLine.includes('GSTN') ||
            nextLine.includes('Currency') ||
            nextLine.includes('*Closing balance includes')) {
          nextLineIndex++;
          continue;
        }
        
        // Stop if we hit an empty line or a line that looks like a table header
        if (nextLine.length === 0 || 
            nextLine.includes('Date') && nextLine.includes('Narration') ||
            nextLine.includes('Withdrawal Amt') ||
            nextLine.includes('Deposit Amt') ||
            nextLine.includes('Closing Balance')) {
          break;
        }
        
        // Add lines that look like transaction continuation
        // This includes reference numbers, additional narration, etc.
        if (nextLine.length > 0 && nextLine.length < 150) {
          fullLine += ' ' + nextLine;
          addedLines++;
          console.log(`Added continuation line ${addedLines}: ${nextLine}`);
        }
        
        nextLineIndex++;
      }
      
      console.log(`Full transaction line (${addedLines} continuation lines added):`, fullLine);
      
      // Debug: Check for the specific 9,078.00 transaction
      if (fullLine.includes('9,078') || fullLine.includes('9078') || fullLine.includes('99,215')) {
        console.log('*** FOUND 9,078.00 TRANSACTION LINE ***');
        console.log('Full line:', fullLine);
        console.log('Date:', date);
      }
      
      // Parse HDFC transaction line with detailed field extraction
      const parsedData = this.parseHDFCTransactionLine(fullLine, date);
      if (!parsedData) {
        console.log('Failed to parse HDFC transaction line');
        return null;
      }
      
      const { transactionAmount, closingBalance, narration, refNo, valueDate } = parsedData;
      
      // Improved transaction type determination based on HDFC patterns
      // Use more specific and accurate patterns for credit vs debit detection
      const isCredit = 
        // ACH Credits (incoming money) - handle both "ACH C-" and "ACH-C-" formats
        narration.toLowerCase().includes('ach c-') ||
        narration.toLowerCase().includes('ach-c-') ||
        // NEFT Credits (incoming transfers)
        narration.toLowerCase().includes('neft cr') ||
        // RTGS Credits
        narration.toLowerCase().includes('rtgs cr') ||
        // IMPS Credits
        narration.toLowerCase().includes('imps cr') ||
        // Salary payments
        narration.toLowerCase().includes('salary') ||
        // Dividend/Interest payments
        narration.toLowerCase().includes('dividend') ||
        narration.toLowerCase().includes('interest') ||
        // Other credit indicators
        narration.toLowerCase().includes('deposit') ||
        // Company/employer payments (common patterns)
        narration.toLowerCase().includes('a2aint01') || // Salary indicator
        narration.toLowerCase().includes('appstars') || // Company name
        narration.toLowerCase().includes('powergrid') ||
        narration.toLowerCase().includes('manappuram') ||
        narration.toLowerCase().includes('maithan') ||
        narration.toLowerCase().includes('tata motors') ||
        narration.toLowerCase().includes('edelweiss') ||
        narration.toLowerCase().includes('ibhfl') ||
        // Refunds or reversals
        narration.toLowerCase().includes('refund') ||
        narration.toLowerCase().includes('reversal');
      
      // Everything else is considered a debit (withdrawal/expense)
      // This includes:
      // - NEFT DR (outgoing transfers)
      // - UPI payments (even if they contain "CR" in merchant names)
      // - EMI payments
      // - ACH D- (debits)
      // - ATM withdrawals
      // - Card payments
      
      // Debug the credit detection for ACH transactions
      if (narration.toLowerCase().includes('ach')) {
        console.log('*** ACH TRANSACTION DEBUG ***');
        console.log('Narration:', narration);
        console.log('Contains ach c-:', narration.toLowerCase().includes('ach c-'));
        console.log('Contains ach-c-:', narration.toLowerCase().includes('ach-c-'));
        console.log('Lowercase narration:', narration.toLowerCase());
        console.log('Final isCredit value:', isCredit);
      }
      
      console.log('Transaction type analysis:', {
        narration: narration.substring(0, 50),
        isCredit,
        amount: transactionAmount
      });
      
      let withdrawalAmount: number | null = null;
      let depositAmount: number | null = null;
      
      if (isCredit) {
        depositAmount = transactionAmount;
      } else {
        withdrawalAmount = transactionAmount;
      }
      
      const type: 'credit' | 'debit' = depositAmount !== null ? 'credit' : 'debit';

      const transaction = {
        date: this.formatDate(date),
        narration: narration,
        refNo,
        valueDate: this.formatDate(valueDate),
        withdrawalAmount,
        depositAmount,
        closingBalance,
        type
      };
      
      console.log('✓ Successfully parsed transaction:', {
        date: transaction.date,
        type: transaction.type,
        amount: transaction.withdrawalAmount || transaction.depositAmount,
        narration: transaction.narration.substring(0, 30) + '...',
        balance: transaction.closingBalance
      });
      
      return transaction;
    } catch (error) {
      console.warn('Failed to parse transaction line:', error);
      return null;
    }
  }

  private static parseHDFCTransactionLine(fullLine: string, transactionDate: string): {
    transactionAmount: number;
    closingBalance: number;
    narration: string;
    refNo: string;
    valueDate: string;
  } | null {
    try {
      console.log('Parsing HDFC line:', fullLine);
      
      // Debug: Check for the specific 78,100.00 transaction
      if (fullLine.includes('78,100') || fullLine.includes('78100') || fullLine.includes('188,702')) {
        console.log('*** DEBUGGING 78,100.00 TRANSACTION ***');
        console.log('Full line:', fullLine);
        console.log('Transaction date:', transactionDate);
      }
      
      // Extract the year from transaction date (e.g., "01/04/25" -> "25")
      const dateMatch = transactionDate.match(/\d{2}\/\d{2}\/(\d{2})/);
      if (!dateMatch) {
        console.log('Could not extract year from transaction date:', transactionDate);
        return null;
      }
      const yearMarker = dateMatch[1]; // e.g., "25", "24", "23", etc.
      console.log('Using year marker:', yearMarker);
      
      // Remove the initial transaction date
      let remaining = fullLine.replace(/^\d{2}\/\d{2}\/\d{2,4}/, '').trim();
      console.log('After removing initial date:', remaining);
      
      // HDFC concatenates amounts in a specific pattern: [transaction_details][YY][amount][balance]
      // Find the year marker that is followed by amounts (more selective approach)
      // Look for year marker immediately followed by amount patterns (most reliable)
      const yearMarkerPattern = new RegExp(`(\\d{2}\/\\d{2}\/)?(${yearMarker})([\\d,\\.]+)`, 'g');
      const matches = [...remaining.matchAll(yearMarkerPattern)];
      
      let bestMatch: RegExpMatchArray | null = null;
      let concatenatedAmounts = '';
      let beforeAmounts = '';
      
      // Find the year marker that's immediately followed by amount-like patterns
      // Prioritize matches where the year marker is directly before amount patterns
      for (const match of matches) {
        const afterYearMarker = remaining.substring(match.index! + match[0].length - match[3].length);
        console.log(`Checking year marker at position ${match.index}: "${match[2]}" followed by "${match[3]}"`);
        
        // Check if what follows looks like concatenated amounts (amount.balancepattern)
        // Look for patterns like "78,100.00188,702.88" or "500.00167,293.88"
        if (/^[\d,]+\.\d{2}[\d,]+\.\d{2}/.test(afterYearMarker) || 
            /^[\d,]+\.\d{2}\s/.test(afterYearMarker)) {
          bestMatch = match;
          const yearMarkerStartInFull = match.index! + (match[1] ? match[1].length : 0);
          beforeAmounts = remaining.substring(0, yearMarkerStartInFull).trim();
          concatenatedAmounts = remaining.substring(yearMarkerStartInFull + yearMarker.length);
          console.log(`✓ Selected optimal year marker at position ${yearMarkerStartInFull}`);
          break;
        }
      }
      
      // Fallback: use more sophisticated approach if no optimal match found
      if (!bestMatch) {
        console.log('No optimal year marker found, trying enhanced fallback approach');
        
        // Look for year marker followed by amount patterns more broadly
        // Handle cases where amounts are directly concatenated like "2578,100.00188,702.88"
        const allYearMarkerMatches = [...remaining.matchAll(new RegExp(`${yearMarker}`, 'g'))];
        
        for (const match of allYearMarkerMatches) {
          const afterMarker = remaining.substring(match.index! + yearMarker.length);
          console.log(`Fallback checking position ${match.index}: "${yearMarker}" followed by "${afterMarker.substring(0, 20)}..."`);
          
          // Look for amount patterns immediately after year marker
          // Pattern: 78,100.00188,702.88 or similar
          if (/^[\d,]+\.\d{2}[\d,]+\.\d{2}/.test(afterMarker)) {
            beforeAmounts = remaining.substring(0, match.index!).trim();
            concatenatedAmounts = afterMarker;
            console.log(`✓ Selected fallback year marker at position ${match.index}`);
            bestMatch = match; // Set a dummy match to continue processing
            break;
          }
        }
        
        // Final fallback: use the last occurrence
        if (!bestMatch) {
          console.log('Using last occurrence as final fallback');
          const yearMarkerIndex = remaining.lastIndexOf(yearMarker);
          
          if (yearMarkerIndex === -1) {
            console.log(`Could not find year marker "${yearMarker}" in the line`);
            return null;
          }
          
          beforeAmounts = remaining.substring(0, yearMarkerIndex).trim();
          concatenatedAmounts = remaining.substring(yearMarkerIndex + yearMarker.length);
        }
      }
      
      // Ensure we have valid data to process
      if (!concatenatedAmounts) {
        console.log('No concatenated amounts found after year marker detection');
        return null;
      }
      
      console.log('Before amounts (narration + ref):', beforeAmounts);
      console.log(`Concatenated amounts after ${yearMarker}:`, concatenatedAmounts);
      
      // Parse the concatenated amounts using HDFC-specific logic
      const amounts = this.parseHDFCConcatenatedAmounts(concatenatedAmounts);
      if (!amounts) {
        console.log('Could not parse concatenated amounts');
        return null;
      }
      
      const { transactionAmount, closingBalance } = amounts;
      console.log('Parsed amounts:', { transactionAmount, closingBalance });
      
      // Extract value date (should be the transaction date in most cases for HDFC)
      const valueDate = transactionDate;
      
      // Extract reference number (longest sequence of digits from before amounts)
      const refMatches = beforeAmounts.match(/\d{10,}/g);
      let refNo = '';
      if (refMatches) {
        refNo = refMatches.reduce((longest, current) => 
          current.length > longest.length ? current : longest
        );
      }
      console.log('Found reference number:', refNo);
      
      // Extract narration (remove reference number and clean up)
      let narration = beforeAmounts;
      if (refNo) {
        narration = narration.replace(refNo, '').trim();
      }
      
      // Enhanced narration cleaning - remove all date fragments more comprehensively
      narration = narration
        // Remove various date patterns that might leak into narration
        .replace(/\/\d{2}\/\d{2,4}/g, '')  // Remove full date patterns like /04/25 or /04/2025
        .replace(/\/\d{2}\//g, '')         // Remove partial patterns like /04/
        .replace(/\/\d{2}$/g, '')          // Remove trailing patterns like /04
        .replace(/^\d{2}\//g, '')          // Remove leading patterns like 04/
        .replace(/\s+/g, ' ')             // Normalize whitespace
        .replace(/^[-,\s]+|[-,\s]+$/g, '') // Remove leading/trailing punctuation
        .replace(/\s*-\s*$/, '')          // Remove trailing dashes
        .trim();
      
      // Additional cleaning for HDFC-specific patterns
      narration = narration
        .replace(/^(NEFT|UPI|ACH|EMI|IMPS|RTGS)\s*-?\s*/, '$1-') // Normalize transaction type prefixes
        .replace(/\s*-\s*$/, '')          // Remove trailing dashes again
        .trim();
      
      console.log('Final parsed fields:', {
        transactionAmount,
        closingBalance,
        narration,
        refNo,
        valueDate
      });
      
      return {
        transactionAmount,
        closingBalance,
        narration,
        refNo,
        valueDate
      };
    } catch (error) {
      console.error('Error parsing HDFC transaction line:', error);
      return null;
    }
  }

  private static parseHDFCConcatenatedAmounts(concatenatedStr: string): {
    transactionAmount: number;
    closingBalance: number;
  } | null {
    try {
      console.log('Parsing concatenated amounts:', concatenatedStr);
      
      // Debug: Check for the specific 9,078.00 transaction
      if (concatenatedStr.includes('9,078') || concatenatedStr.includes('9078')) {
        console.log('*** DEBUGGING 9,078.00 TRANSACTION ***');
        console.log('Concatenated string:', concatenatedStr);
      }
      
      // HDFC concatenation patterns analysis:
      // 1. "78,100.00188,702.88" → amount=78,100.00, balance=188,702.88
      // 2. "11,000.00177,702.88 SS0IN0460..." → amount=11,000.00, balance=177,702.88
      // 3. "10,000.00167,793.88 PAY-HDFC..." → amount=10,000.00, balance=167,793.88
      
      // Key insight: Balance is always 6 digits (XXX,XXX.XX format) representing account balance
      // Transaction amount comes before the balance and is typically smaller
      
      // Step 1: Find the closing balance using multiple pattern approaches
      // Remove all artificial limits - a finance tracker should handle ANY amount
      const balancePatterns = [
        // Try balance with comma first (most reliable for account balances)
        /(\d{2,3},\d{3}\.\d{2})/g,     // XX,XXX.XX or XXX,XXX.XX pattern  
        /(\d{5,6}\.\d{2})/g            // XXXXX.XX or XXXXXX.XX pattern (fallback)
      ];
      
      let closingBalance: number = 0;
      let balanceMatch: RegExpMatchArray | null = null;
      let beforeBalance: string = '';
      
      // Try each pattern to find the account balance
      // Look for the RIGHTMOST (last) occurrence as it's most likely the closing balance
      for (const pattern of balancePatterns) {
        const matches = [...concatenatedStr.matchAll(pattern)];
        if (matches.length > 0) {
          // Take the last match as it's most likely the closing balance
          const lastMatch = matches[matches.length - 1];
          const amount = parseFloat(lastMatch[1].replace(/,/g, ''));
          // Remove artificial limits - accept any positive amount
          if (amount > 0) {
            closingBalance = amount;
            balanceMatch = lastMatch;
            beforeBalance = concatenatedStr.substring(0, concatenatedStr.indexOf(lastMatch[1]));
            console.log('Found account balance:', lastMatch[1], '->', closingBalance);
            break;
          }
        }
      }
      
      if (!balanceMatch || closingBalance === 0) {
        console.log('Could not find valid account balance pattern');
        return null;
      }
      
      console.log('Text before balance:', beforeBalance);
      
      // Step 2: Parse transaction amount from text before balance
      let transactionAmount: number;
      
      const cleanBeforeBalance = beforeBalance.trim();
      
      // Extract transaction amount (should be at the end of cleanBeforeBalance)
      if (cleanBeforeBalance.includes(',')) {
        // Case: amount has comma (e.g., "78,100.00" or "11,000.00" or "9,078.00")
        // Handle both 1 and 2 decimal places: 9,784.0 or 9,784.00
        const amountWithCommaMatch = cleanBeforeBalance.match(/(\d{1,10},\d{3}\.\d{1,2})$/);
        if (amountWithCommaMatch) {
          const amount = parseFloat(amountWithCommaMatch[1].replace(/,/g, ''));
          // Remove all artificial limits - accept any positive amount
          if (amount > 0) {
            transactionAmount = amount;
            console.log('Found transaction amount with comma:', amountWithCommaMatch[1], '->', transactionAmount);
          } else {
            console.log('Invalid amount (not positive):', amountWithCommaMatch[1]);
            return null;
          }
        } else {
          console.log('Could not parse amount with comma from:', cleanBeforeBalance);
          return null;
        }
      } else {
        // Case: amount without comma (e.g., "13.00", "500.00", "1500.00", "9078.00")
        // Handle both 1 and 2 decimal places: 472.0 or 472.00
        const amountMatch = cleanBeforeBalance.match(/(\d{1,10}\.\d{1,2})$/);
        if (amountMatch) {
          const amount = parseFloat(amountMatch[1]);
          // Remove all artificial limits - accept any positive amount
          if (amount > 0) {
            transactionAmount = amount;
            console.log('Found transaction amount without comma:', amountMatch[1], '->', transactionAmount);
          } else {
            console.log('Invalid amount (not positive):', amountMatch[1]);
            return null;
          }
        } else {
          console.log('Could not parse amount without comma from:', cleanBeforeBalance);
          return null;
        }
      }
      
      return { transactionAmount, closingBalance };
    } catch (error) {
      console.error('Error parsing concatenated amounts:', error);
      return null;
    }
  }

  private static formatDate(dateStr: string): string {
    // Convert DD/MM/YY to YYYY-MM-DD
    const [day, month, year] = dateStr.split('/');
    const fullYear = year.length === 2 ? `20${year}` : year;
    return `${fullYear}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
  }

  private static calculateSummary(transactions: ParsedTransaction[]) {
    let totalDebits = 0;
    let totalCredits = 0;
    let debitCount = 0;
    let creditCount = 0;

    transactions.forEach(transaction => {
      if (transaction.type === 'debit' && transaction.withdrawalAmount) {
        totalDebits += transaction.withdrawalAmount;
        debitCount++;
      } else if (transaction.type === 'credit' && transaction.depositAmount) {
        totalCredits += transaction.depositAmount;
        creditCount++;
      }
    });

    return {
      totalDebits,
      totalCredits,
      debitCount,
      creditCount
    };
  }
}
