import { z } from 'zod';

// Legacy validation schemas using Zod (kept for backward compatibility)
export const legacyTransactionSchema = z.object({
  amount: z.number().positive('Amount must be positive'),
  description: z.string().optional(),
  category_id: z.string().uuid('Invalid category ID').optional(),
  account_id: z.string().uuid('Invalid account ID').optional(),
  transaction_type: z.enum(['income', 'expense', 'transfer', 'investment_buy', 'investment_sell', 'dividend']),
  transaction_date: z.string().or(z.date()),
  fees: z.number().min(0, 'Fees cannot be negative').optional(),
});

export const categorySchema = z.object({
  name: z.string().min(1, 'Category name is required'),
  icon: z.string().optional(),
  color: z.string().regex(/^#[0-9A-F]{6}$/i, 'Invalid color format').optional(),
  type: z.enum(['income', 'expense', 'transfer']).optional(),
  parent_category_id: z.string().uuid('Invalid parent category ID').optional(),
  sort_order: z.number().int().min(0).optional(),
});

export const accountSchema = z.object({
  name: z.string().min(1, 'Account name is required'),
  account_type: z.enum(['bank', 'investment', 'savings', 'credit_card', 'cash']),
  account_number: z.string().optional(),
  institution_name: z.string().optional(),
  current_balance: z.number().optional(),
  available_balance: z.number().optional(),
  credit_limit: z.number().min(0, 'Credit limit cannot be negative').optional(),
  interest_rate: z.number().min(0).max(100, 'Interest rate must be between 0 and 100').optional(),
  is_primary: z.boolean().optional(),
}).refine((data) => {
  // Only credit cards can have credit limits
  if (data.credit_limit && data.account_type !== 'credit_card') {
    return false
  }
  // Balance cannot be negative unless it's a credit card
  if ((data.current_balance || 0) < 0 && data.account_type !== 'credit_card') {
    return false
  }
  return true
}, {
  message: 'Invalid account configuration: only credit cards can have credit limits or negative balances',
});

export const transferSchema = z.object({
  amount: z.number().positive('Amount must be positive'),
  description: z.string().optional(),
  from_account_id: z.string().uuid('Invalid source account ID'),
  to_account_id: z.string().uuid('Invalid destination account ID'),
  category_id: z.string().uuid('Category is required for transfers'),
  transaction_date: z.string().or(z.date()),
  fees: z.number().min(0, 'Fees cannot be negative').optional(),
}).refine(data => data.from_account_id !== data.to_account_id, {
  message: 'Source and destination accounts must be different',
  path: ['to_account_id'],
});

export const investmentSchema = z.object({
  amount: z.number().positive('Amount must be positive'),
  description: z.string().optional(),
  account_id: z.string().uuid('Invalid account ID'),
  investment_symbol: z.string().min(1, 'Investment symbol is required').max(10, 'Symbol too long'),
  investment_quantity: z.number().positive('Quantity must be positive'),
  investment_price: z.number().positive('Price must be positive'),
  transaction_type: z.enum(['investment_buy', 'investment_sell']),
  transaction_date: z.string().or(z.date()),
  fees: z.number().min(0, 'Fees cannot be negative').optional(),
});

export type TTransactionForm = z.infer<typeof legacyTransactionSchema>;
export type TCategoryForm = z.infer<typeof categorySchema>;
export type TAccountForm = z.infer<typeof accountSchema>;
export type TTransferForm = z.infer<typeof transferSchema>;
export type TInvestmentForm = z.infer<typeof investmentSchema>;