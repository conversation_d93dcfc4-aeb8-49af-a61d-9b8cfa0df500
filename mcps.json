{"mcpServers": {"memory": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"]}, "graphiti-memory": {"command": "npx", "args": ["mcp-remote", "http://localhost:8000/sse"]}, "sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"]}, "claude-code": {"command": "claude", "args": ["mcp", "serve"], "env": {}}, "neo4j-memory": {"command": "/Users/<USER>/.local/bin/uvx", "args": ["mcp-neo4j-cypher", "--db-url", "neo4j://localhost:7687", "--username", "neo4j", "--password", "demodemo"]}}}