// Test for HDFC PDF Parser - Multi-Year Support Test\nconsole.log('=== HDFC PDF Parser Multi-Year Test ===\\n');\n\n// Test transactions from different years\nconst testDataByYear = {\n  2025: {\n    lines: [\n      \"01/04/25NEFT DR-ICIC0001550-ARAVINTH E-NETBANK,HDFCN5202504015026481101/04/2578,100.00188,702.88\",\n      \"02/04/25ACH C- MUTHOOT FINANCE LIMI-7GV219790000000667432393502/04/2513.00177,715.88\"\n    ],\n    expected: [\n      { amount: 78100.00, balance: 188702.88, type: 'debit' },\n      { amount: 13.00, balance: 177715.88, type: 'credit' }\n    ]\n  },\n  2024: {\n    lines: [\n      \"15/03/24SALARY CREDIT-APPSTARS TECH SOLUTIONS PRIVATE LTD123456789012345601542450,000.00195,450.00\",\n      \"16/03/24UPI-DR-GROCERY STORE000987654321098702/03/245,200.00190,250.00\"\n    ],\n    expected: [\n      { amount: 50000.00, balance: 195450.00, type: 'credit' },\n      { amount: 5200.00, balance: 190250.00, type: 'debit' }\n    ]\n  },\n  2023: {\n    lines: [\n      \"10/12/23NEFT CR-DIVIDEND PAYMENT98765432109876543212/10/2315,500.00125,500.00\",\n      \"11/12/23ATM WITHDRAWAL-HDFC0000123456789012345611/12/232,000.00123,500.00\"\n    ],\n    expected: [\n      { amount: 15500.00, balance: 125500.00, type: 'credit' },\n      { amount: 2000.00, balance: 123500.00, type: 'debit' }\n    ]\n  }\n};\n\n// Extract year marker from transaction date\nfunction extractYearMarker(transactionDate) {\n  const match = transactionDate.match(/\\d{2}\\/\\d{2}\\/(\\d{2})/);\n  return match ? match[1] : null;\n}\n\n// Test the concatenated amount parsing logic\nfunction testParseHDFCConcatenatedAmounts(concatenatedStr) {\n  try {\n    console.log('    📊 Parsing:', concatenatedStr);\n    \n    // Step 1: Find the closing balance\n    const balanceMatch = concatenatedStr.match(/(\\d{2,3},\\d{3}\\.\\d{2})$/);\n    \n    let closingBalance;\n    let remainingAfterBalance;\n    \n    if (balanceMatch) {\n      closingBalance = parseFloat(balanceMatch[1].replace(/,/g, ''));\n      remainingAfterBalance = concatenatedStr.substring(0, concatenatedStr.lastIndexOf(balanceMatch[1]));\n      console.log('    ✅ Balance (with comma):', balanceMatch[1], '->', closingBalance);\n    } else {\n      // Handle concatenated balance without comma\n      const concatenatedBalanceMatch = concatenatedStr.match(/(\\d{6}\\.\\d{2})$/);\n      if (concatenatedBalanceMatch) {\n        const balanceStr = concatenatedBalanceMatch[1];\n        const formattedBalance = balanceStr.replace(/(\\d{3})(\\d{3}\\.\\d{2})$/, '$1,$2');\n        closingBalance = parseFloat(formattedBalance.replace(/,/g, ''));\n        remainingAfterBalance = concatenatedStr.substring(0, concatenatedStr.lastIndexOf(balanceStr));\n        console.log('    ✅ Balance (concatenated):', balanceStr, '→', formattedBalance, '->', closingBalance);\n      } else {\n        console.log('    ❌ Could not find balance');\n        return null;\n      }\n    }\n    \n    // Step 2: Parse transaction amount\n    let transactionAmount;\n    \n    if (remainingAfterBalance.includes(',')) {\n      const amountWithCommaMatch = remainingAfterBalance.match(/(\\d{1,2},\\d{3}\\.\\d{2})$/);\n      if (amountWithCommaMatch) {\n        transactionAmount = parseFloat(amountWithCommaMatch[1].replace(/,/g, ''));\n        console.log('    ✅ Amount (with comma):', amountWithCommaMatch[1], '->', transactionAmount);\n      } else {\n        console.log('    ❌ Could not parse amount with comma');\n        return null;\n      }\n    } else {\n      const amountMatch = remainingAfterBalance.match(/(\\d{1,5}\\.\\d{2})$/);\n      if (amountMatch) {\n        transactionAmount = parseFloat(amountMatch[1]);\n        console.log('    ✅ Amount (no comma):', amountMatch[1], '->', transactionAmount);\n      } else {\n        console.log('    ❌ Could not parse amount');\n        return null;\n      }\n    }\n    \n    return { transactionAmount, closingBalance };\n  } catch (error) {\n    console.error('    ❌ Error:', error.message);\n    return null;\n  }\n}\n\n// Main parsing test function\nfunction testParseHDFCTransactionLine(fullLine, transactionDate) {\n  try {\n    // Extract year marker\n    const yearMarker = extractYearMarker(transactionDate);\n    if (!yearMarker) {\n      console.log('  ❌ Could not extract year from:', transactionDate);\n      return null;\n    }\n    console.log('  📅 Year marker:', yearMarker);\n    \n    // Remove initial date\n    let remaining = fullLine.replace(/^\\d{2}\\/\\d{2}\\/\\d{2,4}/, '').trim();\n    console.log('  📝 After removing date:', remaining.substring(0, 50) + '...');\n    \n    // Find year marker\n    const yearMarkerIndex = remaining.lastIndexOf(yearMarker);\n    if (yearMarkerIndex === -1) {\n      console.log(`  ❌ Could not find year marker \"${yearMarker}\"`);\n      return null;\n    }\n    \n    // Extract concatenated amounts\n    const concatenatedAmounts = remaining.substring(yearMarkerIndex + yearMarker.length);\n    console.log(`  🔗 Amounts after \"${yearMarker}\":`, concatenatedAmounts);\n    \n    // Parse amounts\n    const amounts = testParseHDFCConcatenatedAmounts(concatenatedAmounts);\n    if (!amounts) {\n      console.log('  ❌ Failed to parse amounts');\n      return null;\n    }\n    \n    return amounts;\n  } catch (error) {\n    console.error('  ❌ Error:', error.message);\n    return null;\n  }\n}\n\n// Run tests for all years\nconsole.log('🧪 Testing parser with transactions from different years...\\n');\n\nlet totalTests = 0;\nlet passedTests = 0;\n\nObject.entries(testDataByYear).forEach(([year, data]) => {\n  console.log(`\\n=== 📅 Testing Year 20${data.lines[0].match(/\\/(\\d{2})/)[1]} ===`);\n  \n  data.lines.forEach((line, index) => {\n    totalTests++;\n    const dateMatch = line.match(/^(\\d{2}\\/\\d{2}\\/\\d{2})/);\n    \n    if (dateMatch) {\n      console.log(`\\n🔍 Test ${totalTests}: Transaction ${index + 1}`);\n      console.log('📄 Input:', line.substring(0, 60) + '...');\n      \n      const result = testParseHDFCTransactionLine(line, dateMatch[1]);\n      const expected = data.expected[index];\n      \n      if (result) {\n        const amountCorrect = Math.abs(result.transactionAmount - expected.amount) < 0.01;\n        const balanceCorrect = Math.abs(result.closingBalance - expected.balance) < 0.01;\n        \n        console.log(`  💰 Amount: Expected ${expected.amount.toLocaleString()} vs Parsed ${result.transactionAmount.toLocaleString()} ${amountCorrect ? '✅' : '❌'}`);\n        console.log(`  💳 Balance: Expected ${expected.balance.toLocaleString()} vs Parsed ${result.closingBalance.toLocaleString()} ${balanceCorrect ? '✅' : '❌'}`);\n        \n        if (amountCorrect && balanceCorrect) {\n          console.log('  🎉 PASS: Transaction parsed correctly!');\n          passedTests++;\n        } else {\n          console.log('  ⚠️  FAIL: Amounts incorrect');\n        }\n      } else {\n        console.log('  ❌ FAIL: Could not parse transaction');\n      }\n    }\n  });\n});\n\nconsole.log(`\\n=== 📊 Test Results ===`);\nconsole.log(`Total Tests: ${totalTests}`);\nconsole.log(`Passed: ${passedTests} ✅`);\nconsole.log(`Failed: ${totalTests - passedTests} ❌`);\nconsole.log(`Success Rate: ${Math.round((passedTests / totalTests) * 100)}%`);\n\nif (passedTests === totalTests) {\n  console.log('\\n🎉 All tests passed! The parser works across different years!');\n} else {\n  console.log('\\n⚠️  Some tests failed. The parser may need further refinement.');\n}\n\nconsole.log('\\n📝 This test verifies that the parser correctly extracts year markers');\nconsole.log('   from transaction dates and handles concatenated amounts for any year.');\n