import { NextRequest, NextResponse } from 'next/server';
import { BankStatementParser } from '@repo/shared/src/lib/pdf-parser';

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('file');
    
    if (!(file instanceof File)) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      );
    }
    
    if (file.type !== 'application/pdf') {
      return NextResponse.json(
        { error: 'File must be a PDF' },
        { status: 400 }
      );
    }
    
    if (file.size > 10 * 1024 * 1024) { // 10MB limit
      return NextResponse.json(
        { error: 'File size must be less than 10MB' },
        { status: 400 }
      );
    }
    
    const buffer = Buffer.from(await file.arrayBuffer());
    
    // Store original cwd and temporarily change to node_modules/pdf-parse to fix path issues
    const originalCwd = process.cwd();
    const path = await import('path');
    const pdfParseDir = path.join(originalCwd, '..', '..', 'node_modules', 'pdf-parse');
    
    try {
      process.chdir(pdfParseDir);
      const parsedData = await BankStatementParser.parsePDF(buffer);
      return NextResponse.json({
        success: true,
        data: parsedData
      });
    } finally {
      // Always restore original cwd
      process.chdir(originalCwd);
    }
    
  } catch (error) {
    console.error('PDF parsing error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to parse PDF',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
