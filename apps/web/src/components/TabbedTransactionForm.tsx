import React, { useState, useEffect } from 'react'
import { useForm, Controller } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import {
  comprehensiveTransactionFormInputSchema,
  comprehensiveTransactionFormSchema,
  type ComprehensiveTransactionFormInputData,
  type ICategory,
  type IAccount,
  AccountService,
  CategoryService,
  TransferService,
  useCurrencyStore
} from '@repo/shared'
import { LoadingState } from './LoadingSpinner'
import toast from 'react-hot-toast'

interface TabbedTransactionFormProps {
  onSubmit: (data: any) => Promise<void>
  loading?: boolean
  className?: string
  initialData?: any
  compact?: boolean
  isEditing?: boolean
}

type TabType = 'expense' | 'income' | 'transfer'

export const TabbedTransactionForm: React.FC<TabbedTransactionFormProps> = ({
  onSubmit,
  loading = false,
  className = "",
  initialData,
  compact = false,
  isEditing = false
}) => {
  const [activeTab, setActiveTab] = useState<TabType>(initialData?.transaction_type || 'expense')
  const [categories, setCategories] = useState<ICategory[]>([])
  const [transferCategories, setTransferCategories] = useState<ICategory[]>([])
  const [accounts, setAccounts] = useState<IAccount[]>([])
  const [loadingData, setLoadingData] = useState(true)
  const { formatCurrency } = useCurrencyStore()

  const form = useForm<ComprehensiveTransactionFormInputData>({
    resolver: zodResolver(comprehensiveTransactionFormInputSchema),
    defaultValues: {
      amount: initialData?.amount?.toString() || '',
      category_id: initialData?.category_id || '',
      account_id: initialData?.account_id || '',
      to_account_id: initialData?.to_account_id || '',
      description: initialData?.description || '',
      transaction_date: initialData?.transaction_date || new Date(),
      transaction_type: initialData?.transaction_type || 'expense',
      fees: initialData?.fees?.toString() || '',
      investment_symbol: initialData?.investment_symbol || '',
      investment_quantity: initialData?.investment_quantity?.toString() || '',
      investment_price: initialData?.investment_price?.toString() || '',
      funding_account_id: initialData?.funding_account_id || '',
      is_internal: initialData?.is_internal ?? true, // Default to true (exclude from analytics)
    }
  })

  const {
    control,
    handleSubmit,
    reset,
    setValue,
    watch,
    formState: { errors, isSubmitting }
  } = form

  const transactionType = watch('transaction_type')
  const accountId = watch('account_id')

  const loadData = async () => {
    try {
      setLoadingData(true)
      const [categoriesData, transferCategoriesData, accountsData] = await Promise.all([
        CategoryService.getCategories({ is_active: true }),
        TransferService.getTransferCategories(),
        AccountService.getAccounts({ is_active: true })
      ])
      setCategories(categoriesData)
      setTransferCategories(transferCategoriesData)
      
      // If no accounts exist, create default account
      if (accountsData.length === 0) {
        console.log('No accounts found, creating default account...')
        try {
          const defaultAccounts = await AccountService.createDefaultAccounts()
          setAccounts(defaultAccounts)
          
          if (defaultAccounts.length > 0) {
            toast.success('Created your first Cash account!')
            setValue('account_id', defaultAccounts[0].id)
          } else {
            toast.error('Failed to create default account. Please create an account manually.')
          }
        } catch (error) {
          console.error('Error creating default accounts:', error)
          toast.error('Failed to create default account. Please create an account manually.')
        }
      } else {
        setAccounts(accountsData)

        // Auto-select first account if editing and no account is selected
        if (initialData && !initialData.account_id && accountsData.length > 0) {
          setValue('account_id', accountsData[0].id)
        }
      }
    } catch (error) {
      console.error('Error loading form data:', error)
      toast.error('Failed to load form data')
    } finally {
      setLoadingData(false)
    }
  }

  useEffect(() => {
    loadData()
  }, [])

  // Auto-refresh categories when window regains focus (after creating category in new tab)
  useEffect(() => {
    let timeoutId: NodeJS.Timeout | null = null

    const handleWindowFocus = () => {
      // Debounce the refresh to avoid multiple calls
      if (timeoutId) clearTimeout(timeoutId)
      
      timeoutId = setTimeout(async () => {
        try {
          // Only refresh categories and transfer categories, not accounts
          const [categoriesData, transferCategoriesData] = await Promise.all([
            CategoryService.getCategories({ is_active: true }),
            TransferService.getTransferCategories()
          ])
          setCategories(categoriesData)
          setTransferCategories(transferCategoriesData)
        } catch (error) {
          console.error('Error refreshing categories on focus:', error)
        }
      }, 500) // 500ms debounce
    }

    window.addEventListener('focus', handleWindowFocus)
    return () => {
      window.removeEventListener('focus', handleWindowFocus)
      if (timeoutId) clearTimeout(timeoutId)
    }
  }, [])

  // Update form when initialData changes (for template usage)
  useEffect(() => {
    if (initialData && !loadingData) {
      // Only reset when we have initial data and we're not loading
      reset({
        amount: initialData.amount?.toString() || '',
        category_id: initialData.category_id || '',
        account_id: initialData.account_id || '',
        to_account_id: initialData.to_account_id || '',
        description: initialData.description || '',
        transaction_date: initialData.transaction_date || new Date(),
        transaction_type: initialData.transaction_type || 'expense',
        fees: initialData.fees?.toString() || '',
        investment_symbol: initialData.investment_symbol || '',
        investment_quantity: initialData.investment_quantity?.toString() || '',
        investment_price: initialData.investment_price?.toString() || '',
        funding_account_id: initialData.funding_account_id || '',
        is_internal: initialData.is_internal ?? true,
      })
      setActiveTab(initialData.transaction_type || 'expense')
    }
  }, [initialData, reset, loadingData])

  // Update transaction type when tab changes
  useEffect(() => {
    setValue('transaction_type', activeTab)
  }, [activeTab, setValue])

  // Filter accounts based on transaction type
  const getFilteredAccounts = (purpose: 'source' | 'destination') => {
    switch (purpose) {
      case 'destination':
        return accounts.filter(acc => acc.id !== accountId && acc.account_type !== 'investment')
      default:
        return accounts.filter(acc => acc.account_type !== 'investment')
    }
  }

  // Filter categories based on transaction type
  const getFilteredCategories = () => {
    if (activeTab === 'expense') return categories.filter(cat => cat.type === 'expense')
    if (activeTab === 'income') return categories.filter(cat => cat.type === 'income')
    if (activeTab === 'transfer') return transferCategories
    return categories
  }

  const handleFormSubmit = async (data: ComprehensiveTransactionFormInputData) => {
    try {
      const validatedData = comprehensiveTransactionFormSchema.parse(data)
      await onSubmit(validatedData)
      // Only reset form if submission was successful and it's not an edit
      if (!initialData) {
        reset()
      }
    } catch (error) {
      console.error('Error submitting transaction:', error)
      // Don't reset form on error - keep user's input values
      // The error will be handled by the parent component
      throw error // Re-throw to let parent handle the error display
    }
  }

  if (loadingData) {
    return <LoadingState className="h-32" message="Loading form data..." />
  }

  const tabs = [
    { id: 'expense' as TabType, label: 'Expense', icon: '💸' },
    { id: 'income' as TabType, label: 'Income', icon: '💰' },
    { id: 'transfer' as TabType, label: 'Transfer', icon: '🔄' }
  ]

  return (
    <div className={`${className}`}>
      {/* Tab Navigation */}
      <div className="flex space-x-1 bg-surface rounded-lg p-1 mb-6">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            type="button"
            onClick={() => setActiveTab(tab.id)}
            className={`flex-1 flex items-center justify-center space-x-2 px-4 py-3 rounded-md text-sm font-medium transition-all ${
              activeTab === tab.id
                ? 'bg-primary-blue text-white shadow-sm'
                : 'text-text-secondary hover:text-text-primary hover:bg-surface'
            }`}
          >
            <span>{tab.icon}</span>
            <span>{tab.label}</span>
          </button>
        ))}
      </div>

      {/* Form Content */}
      <form onSubmit={handleSubmit(handleFormSubmit)} className={`${compact ? 'space-y-4' : 'space-y-6'}`}>
        {/* Amount */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-text-primary">
            Amount *
          </label>
          <Controller
            name="amount"
            control={control}
            render={({ field }) => (
              <input
                type="text"
                inputMode="decimal"
                {...field}
                placeholder="0.00"
                className={`w-full bg-surface border-2 border-border rounded-lg px-4 py-3 text-base transition-all focus:border-primary-blue focus:outline-none focus:ring-2 focus:ring-primary-blue/20 text-text-primary ${errors.amount ? 'border-error-red' : ''}`}
              />
            )}
          />
          {errors.amount && (
            <p className="text-sm text-error-red">{errors.amount.message}</p>
          )}
        </div>

        {/* Account Selection */}
        {(activeTab === 'expense' || activeTab === 'income') && (
          <div className="space-y-2">
            <label className="block text-sm font-medium text-text-primary">
              Account *
            </label>
            <Controller
              name="account_id"
              control={control}
              render={({ field }) => (
                <select
                  {...field}
                  className={`w-full bg-surface border-2 border-border rounded-lg px-4 py-3 text-base transition-all focus:border-primary-blue focus:outline-none focus:ring-2 focus:ring-primary-blue/20 text-text-primary ${errors.account_id ? 'border-error-red' : ''}`}
                >
                  <option value="">Select an account</option>
                  {getFilteredAccounts('source').map((account) => (
                    <option key={account.id} value={account.id}>
                      {account.name} ({account.account_type}) - {formatCurrency(account.current_balance || 0)}
                    </option>
                  ))}
                </select>
              )}
            />
            {errors.account_id && (
              <p className="text-sm text-error-red">{errors.account_id.message}</p>
            )}
          </div>
        )}

        {/* Transfer-specific fields */}
        {activeTab === 'transfer' && (
          <>
            <div className="space-y-2">
              <label className="block text-sm font-medium text-text-primary">
                From Account *
              </label>
              <Controller
                name="account_id"
                control={control}
                render={({ field }) => (
                  <select
                    {...field}
                    className={`w-full bg-surface border-2 border-border rounded-lg px-4 py-3 text-base transition-all focus:border-primary-blue focus:outline-none focus:ring-2 focus:ring-primary-blue/20 text-text-primary ${errors.account_id ? 'border-error-red' : ''}`}
                  >
                    <option value="">Select source account</option>
                    {getFilteredAccounts('source').map((account) => (
                      <option key={account.id} value={account.id}>
                        {account.name} ({account.account_type}) - {formatCurrency(account.current_balance || 0)}
                      </option>
                    ))}
                  </select>
                )}
              />
              {errors.account_id && (
                <p className="text-sm text-error-red">{errors.account_id.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <label className="block text-sm font-medium text-text-primary">
                To Account *
              </label>
              <Controller
                name="to_account_id"
                control={control}
                render={({ field }) => (
                  <select
                    {...field}
                    className={`w-full bg-surface border-2 border-border rounded-lg px-4 py-3 text-base transition-all focus:border-primary-blue focus:outline-none focus:ring-2 focus:ring-primary-blue/20 text-text-primary ${errors.to_account_id ? 'border-error-red' : ''}`}
                  >
                    <option value="">Select destination account</option>
                    {getFilteredAccounts('destination').map((account) => (
                      <option key={account.id} value={account.id}>
                        {account.name} ({account.account_type}) - {formatCurrency(account.current_balance || 0)}
                      </option>
                    ))}
                  </select>
                )}
              />
              {errors.to_account_id && (
                <p className="text-sm text-error-red">{errors.to_account_id.message}</p>
              )}
            </div>

            {/* Category Selection for Transfer */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <label className="block text-sm font-medium text-text-primary">
                  Transfer Category *
                </label>
                <button
                  type="button"
                  onClick={() => window.open('/categories', '_blank')}
                  className="text-xs text-primary-blue hover:text-primary-blue/80 font-medium flex items-center gap-1"
                >
                  <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                  </svg>
                  Add Category
                </button>
              </div>
              <Controller
                name="category_id"
                control={control}
                render={({ field }) => (
                  <select
                    {...field}
                    className={`w-full bg-surface border-2 border-border rounded-lg px-4 py-3 text-base transition-all focus:border-primary-blue focus:outline-none focus:ring-2 focus:ring-primary-blue/20 text-text-primary ${errors.category_id ? 'border-error-red' : ''}`}
                  >
                    <option value="">Select a transfer category</option>
                    {getFilteredCategories().map((category) => (
                      <option key={category.id} value={category.id}>
                        {category.name}
                      </option>
                    ))}
                  </select>
                )}
              />
              {getFilteredCategories().length === 0 && (
                <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-3">
                  <div className="flex items-start gap-2">
                    <svg className="w-4 h-4 text-blue-500 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <div className="text-xs text-blue-700 dark:text-blue-300">
                      <p className="font-medium mb-1">No transfer categories found</p>
                      <p className="mb-2">Create a transfer category first. Don't use income or expense categories for transfers.</p>
                      <button
                        type="button"
                        onClick={() => window.open('/categories', '_blank')}
                        className="bg-blue-600 hover:bg-blue-700 text-white text-xs px-3 py-1 rounded-md font-medium"
                      >
                        Create Transfer Category
                      </button>
                    </div>
                  </div>
                </div>
              )}
              {errors.category_id && (
                <p className="text-sm text-error-red">{errors.category_id.message}</p>
              )}
            </div>

            {/* Transfer Fees */}
            <div className="space-y-2">
              <label className="block text-sm font-medium text-text-primary">
                Transfer Fees
              </label>
              <Controller
                name="fees"
                control={control}
                render={({ field }) => (
                  <input
                    type="text"
                    inputMode="decimal"
                    {...field}
                    placeholder="0.00"
                    className={`w-full bg-surface border-2 border-border rounded-lg px-4 py-3 text-base transition-all focus:border-primary-blue focus:outline-none focus:ring-2 focus:ring-primary-blue/20 text-text-primary ${errors.fees ? 'border-error-red' : ''}`}
                  />
                )}
              />
              {errors.fees && (
                <p className="text-sm text-error-red">{errors.fees.message}</p>
              )}
            </div>

            {/* Internal Transfer Checkbox */}
            <div className="space-y-2">
              <div className="flex items-center gap-3">
                <Controller
                  name="is_internal"
                  control={control}
                  render={({ field }) => (
                    <input
                      type="checkbox"
                      checked={field.value ?? true}
                      onChange={(e) => field.onChange(e.target.checked)}
                      className="w-4 h-4 text-primary-blue bg-surface border-2 border-border rounded focus:ring-primary-blue focus:ring-2 focus:ring-primary-blue/20"
                    />
                  )}
                />
                <div className="flex-1">
                  <label className="text-sm font-medium text-text-primary cursor-pointer">
                    Internal Transfer (exclude from analytics)
                  </label>
                  <p className="text-xs text-text-secondary mt-1">
                    Check this for simple account-to-account moves. Uncheck for investment funding, loan payments, or external transfers you want to track.
                  </p>
                </div>
              </div>
            </div>
          </>
        )}

        {/* Fees (for expense transactions) */}
        {activeTab === 'expense' && (
          <div className="space-y-2">
            <label className="block text-sm font-medium text-text-primary">
              Transaction Fees
            </label>
            <Controller
              name="fees"
              control={control}
              render={({ field }) => (
                <input
                  type="text"
                  inputMode="decimal"
                  {...field}
                  placeholder="0.00"
                  className={`w-full bg-surface border-2 border-border rounded-lg px-4 py-3 text-base transition-all focus:border-primary-blue focus:outline-none focus:ring-2 focus:ring-primary-blue/20 text-text-primary ${errors.fees ? 'border-error-red' : ''}`}
                />
              )}
            />
            {errors.fees && (
              <p className="text-sm text-error-red">{errors.fees.message}</p>
            )}
            <p className="text-xs text-text-secondary">
              Add any fees associated with this expense (ATM fees, credit card fees, etc.)
            </p>
          </div>
        )}

        {/* Category Selection (for expense and income) */}
        {(activeTab === 'expense' || activeTab === 'income') && (
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <label className="block text-sm font-medium text-text-primary">
                Category *
              </label>
              <button
                type="button"
                onClick={() => window.open('/categories', '_blank')}
                className="text-xs text-primary-blue hover:text-primary-blue/80 font-medium flex items-center gap-1"
              >
                <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                </svg>
                Add Category
              </button>
            </div>
            <Controller
              name="category_id"
              control={control}
              render={({ field }) => (
                <select
                  {...field}
                  className={`w-full bg-surface border-2 border-border rounded-lg px-4 py-3 text-base transition-all focus:border-primary-blue focus:outline-none focus:ring-2 focus:ring-primary-blue/20 text-text-primary ${errors.category_id ? 'border-error-red' : ''}`}
                >
                  <option value="">Select a category</option>
                  {getFilteredCategories().map((category) => (
                    <option key={category.id} value={category.id}>
                      {category.name}
                    </option>
                  ))}
                </select>
              )}
            />
            {getFilteredCategories().length === 0 && (
              <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-3">
                <div className="flex items-start gap-2">
                  <svg className="w-4 h-4 text-blue-500 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <div className="text-xs text-blue-700 dark:text-blue-300">
                    <p className="font-medium mb-1">No {activeTab} categories found</p>
                    <p className="mb-2">Create {activeTab === 'expense' ? 'an expense' : 'an income'} category first to track your {activeTab}s.</p>
                    <button
                      type="button"
                      onClick={() => window.open('/categories', '_blank')}
                      className="bg-blue-600 hover:bg-blue-700 text-white text-xs px-3 py-1 rounded-md font-medium"
                    >
                      Create {activeTab === 'expense' ? 'Expense' : 'Income'} Category
                    </button>
                  </div>
                </div>
              </div>
            )}
            {errors.category_id && (
              <p className="text-sm text-error-red">{errors.category_id.message}</p>
            )}
          </div>
        )}

        {/* Description */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-text-primary">
            Description
          </label>
          <Controller
            name="description"
            control={control}
            render={({ field }) => (
              <textarea
                {...field}
                rows={3}
                placeholder="Add a note about this transaction..."
                className="w-full bg-surface border-2 border-border rounded-lg px-4 py-3 text-base transition-all focus:border-primary-blue focus:outline-none focus:ring-2 focus:ring-primary-blue/20 text-text-primary resize-none"
              />
            )}
          />
        </div>

        {/* Transaction Date */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-text-primary">
            Date *
          </label>
          <Controller
            name="transaction_date"
            control={control}
            render={({ field }) => (
              <input
                type="date"
                {...field}
                value={(() => {
                  if (!field.value) return '';
                  if (field.value instanceof Date && !isNaN(field.value.getTime())) {
                    return field.value.toISOString().split('T')[0];
                  }
                  if (typeof field.value === 'string') {
                    const date = new Date(field.value);
                    return !isNaN(date.getTime()) ? date.toISOString().split('T')[0] : '';
                  }
                  return '';
                })()}
                onChange={(e) => field.onChange(new Date(e.target.value))}
                className={`w-full bg-surface border-2 border-border rounded-lg px-4 py-3 text-base transition-all focus:border-primary-blue focus:outline-none focus:ring-2 focus:ring-primary-blue/20 text-text-primary ${errors.transaction_date ? 'border-error-red' : ''}`}
              />
            )}
          />
          {errors.transaction_date && (
            <p className="text-sm text-error-red">{errors.transaction_date.message}</p>
          )}
        </div>

        {/* Submit Button */}
        <div className="pt-4">
          <button
            type="submit"
            disabled={isSubmitting || loading}
            className="w-full bg-primary-blue hover:bg-primary-blue/90 disabled:bg-primary-blue/50 text-white font-medium py-3 px-4 rounded-lg transition-colors"
          >
            {isSubmitting || loading ? 'Saving...' : isEditing ? 'Update Transaction' : 'Add Transaction'}
          </button>
        </div>
      </form>
    </div>
  )
}
