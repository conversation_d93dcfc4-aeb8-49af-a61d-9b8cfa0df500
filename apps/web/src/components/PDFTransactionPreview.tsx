'use client';

import React, { useState, useEffect } from 'react';
import { Check, X, AlertCircle, DollarSign } from 'lucide-react';
import { 
  BankStatementData, 
  ParsedTransaction,
  ICategory,
  IAccount,
  TransactionService,
  CategoryService,
  AccountService,
  type TransactionData
} from '@repo/shared';
import { toast } from 'react-hot-toast';

interface ValidationError {
  field: string;
  message: string;
}

interface EnrichedTransaction extends ParsedTransaction {
  id: string;
  enriched_category_id?: string;
  enriched_account_id?: string;
  enriched_description?: string;
  validation_errors: ValidationError[];
  is_valid: boolean;
}

interface PDFTransactionPreviewProps {
  data: BankStatementData;
  onImportComplete: () => void;
  onCancel: () => void;
}

export default function PDFTransactionPreview({ 
  data, 
  onImportComplete, 
  onCancel 
}: PDFTransactionPreviewProps) {
  const [transactions, setTransactions] = useState<EnrichedTransaction[]>([]);
  const [categories, setCategories] = useState<ICategory[]>([]);
  const [accounts, setAccounts] = useState<IAccount[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedTransactions, setSelectedTransactions] = useState<Set<string>>(new Set());

  useEffect(() => {
    initializeData();
  }, [data]);

  const initializeData = async () => {
    try {
      // Load categories and accounts
      const [categoriesData, accountsData] = await Promise.all([
        CategoryService.getCategories({ is_active: true }),
        AccountService.getAccounts({ is_active: true })
      ]);
      
      setCategories(categoriesData);
      setAccounts(accountsData);

      // Convert parsed transactions to enriched format
      const enrichedTransactions: EnrichedTransaction[] = data.transactions.map((transaction, index) => ({
        ...transaction,
        id: `pdf-${index}`,
        validation_errors: [],
        is_valid: false,
        enriched_description: transaction.narration || '',
      }));

      // Auto-assign categories based on narration keywords
      const autoEnrichedTransactions = enrichedTransactions.map(transaction => 
        autoAssignCategory(transaction, categoriesData)
      );

      // Auto-assign accounts based on bank name (HDFC)
      const accountEnrichedTransactions = autoEnrichedTransactions.map(transaction => 
        autoAssignAccount(transaction, accountsData, data)
      );

      // Validate all transactions
      const validatedTransactions = accountEnrichedTransactions.map(validateTransaction);
      
      setTransactions(validatedTransactions);
      
      // Select all valid transactions by default
      const validTransactionIds = new Set(
        validatedTransactions
          .filter(t => t.is_valid)
          .map(t => t.id)
      );
      setSelectedTransactions(validTransactionIds);
      
    } catch (error) {
      console.error('Failed to initialize transaction preview:', error);
      toast.error('Failed to load transaction data');
    }
  };

  const autoAssignCategory = (transaction: EnrichedTransaction, categories: ICategory[]): EnrichedTransaction => {
    const narration = transaction.narration?.toLowerCase() || '';
    
    // Define keyword mappings for common categories
    const categoryMappings = [
      { keywords: ['salary', 'pay', 'payroll'], type: 'income', category: 'Salary' },
      { keywords: ['grocery', 'supermarket', 'food', 'restaurant'], type: 'expense', category: 'Food & Dining' },
      { keywords: ['fuel', 'petrol', 'gas station'], type: 'expense', category: 'Transportation' },
      { keywords: ['atm', 'withdrawal'], type: 'expense', category: 'Cash Withdrawal' },
      { keywords: ['transfer', 'neft', 'imps', 'rtgs'], type: 'transfer', category: 'Transfer' },
      { keywords: ['dividend', 'interest'], type: 'income', category: 'Investment Income' },
      { keywords: ['utility', 'electricity', 'water'], type: 'expense', category: 'Utilities' },
      { keywords: ['medical', 'hospital', 'pharmacy'], type: 'expense', category: 'Healthcare' },
    ];

    for (const mapping of categoryMappings) {
      if (mapping.keywords.some(keyword => narration.includes(keyword))) {
        const matchedCategory = categories.find(
          cat => cat.name.toLowerCase().includes(mapping.category.toLowerCase()) && 
                 cat.type === mapping.type
        );
        if (matchedCategory) {
          return {
            ...transaction,
            enriched_category_id: matchedCategory.id,
          };
        }
      }
    }

    return transaction;
  };

  const autoAssignAccount = (
    transaction: EnrichedTransaction, 
    accounts: IAccount[], 
    statementData: BankStatementData
  ): EnrichedTransaction => {
    // Try to match HDFC account based on various criteria
    let matchedAccount: IAccount | undefined;
    
    // First, try to find account that contains 'HDFC' in the name
    matchedAccount = accounts.find(account => 
      account.name.toLowerCase().includes('hdfc') ||
      account.name.toLowerCase().includes('hdfc bank')
    );
    
    // If no direct HDFC match, try to match by account number
    if (!matchedAccount && statementData.accountNumber) {
      const statementAccountNumber = statementData.accountNumber;
      matchedAccount = accounts.find(account => 
        account.account_number === statementAccountNumber ||
        account.account_number?.includes(statementAccountNumber.slice(-4)) // Last 4 digits
      );
    }
    
    // If still no match, look for accounts with 'bank' or 'saving' in the name as fallback
    if (!matchedAccount) {
      matchedAccount = accounts.find(account => 
        account.name.toLowerCase().includes('bank') ||
        account.name.toLowerCase().includes('saving') ||
        account.name.toLowerCase().includes('savings')
      );
    }
    
    console.log('Auto-assigned account for transaction:', {
      transactionId: transaction.id,
      accountName: matchedAccount?.name,
      accountId: matchedAccount?.id,
      statementAccount: statementData.accountNumber
    });
    
    return {
      ...transaction,
      enriched_account_id: matchedAccount?.id,
    };
  };

  const validateTransaction = (transaction: EnrichedTransaction): EnrichedTransaction => {
    const errors: ValidationError[] = [];

    // Check required fields
    if (!transaction.enriched_category_id) {
      errors.push({ field: 'category', message: 'Category is required' });
    }

    if (!transaction.enriched_account_id) {
      errors.push({ field: 'account', message: 'Account is required' });
    }

    if (!transaction.enriched_description?.trim()) {
      errors.push({ field: 'description', message: 'Description is required' });
    }

    // Validate amounts
    if (!transaction.withdrawalAmount && !transaction.depositAmount) {
      errors.push({ field: 'amount', message: 'Amount is required' });
    }

    // Validate date
    if (!transaction.date) {
      errors.push({ field: 'date', message: 'Date is required' });
    }

    return {
      ...transaction,
      validation_errors: errors,
      is_valid: errors.length === 0,
    };
  };

  const updateTransaction = (id: string, updates: Partial<EnrichedTransaction>) => {
    setTransactions(prev => prev.map(transaction => {
      if (transaction.id === id) {
        const updated = { ...transaction, ...updates };
        return validateTransaction(updated);
      }
      return transaction;
    }));
  };

  const toggleTransactionSelection = (id: string) => {
    setSelectedTransactions(prev => {
      const newSet = new Set(prev);
      if (newSet.has(id)) {
        newSet.delete(id);
      } else {
        newSet.add(id);
      }
      return newSet;
    });
  };

  const selectAllValid = () => {
    const validIds = transactions
      .filter(t => t.is_valid)
      .map(t => t.id);
    setSelectedTransactions(new Set(validIds));
  };

  const deselectAll = () => {
    setSelectedTransactions(new Set());
  };

  const handleSubmit = async () => {
    const selectedValidTransactions = transactions.filter(
      t => selectedTransactions.has(t.id) && t.is_valid
    );

    if (selectedValidTransactions.length === 0) {
      toast.error('Please select at least one valid transaction to import');
      return;
    }

    setIsSubmitting(true);
    try {
      for (const transaction of selectedValidTransactions) {
        const transactionData: TransactionData = {
          amount: transaction.withdrawalAmount || transaction.depositAmount || 0,
          description: transaction.enriched_description || transaction.narration || '',
          category_id: transaction.enriched_category_id!,
          transaction_date: new Date(transaction.date),
          transaction_type: transaction.type === 'credit' ? 'income' : 'expense',
        };

        await TransactionService.createTransaction(transactionData);
      }

      toast.success(`Successfully imported ${selectedValidTransactions.length} transactions!`);
      onImportComplete();
    } catch (error) {
      console.error('Failed to import transactions:', error);
      toast.error('Failed to import transactions. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const formatCurrency = (amount: number | null) => {
    if (amount === null) return '-';
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR'
    }).format(amount);
  };

  const validTransactionsCount = transactions.filter(t => t.is_valid).length;
  const selectedValidCount = transactions.filter(t => selectedTransactions.has(t.id) && t.is_valid).length;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h3 className="text-lg font-semibold text-blue-900 mb-2">
          PDF Transaction Preview
        </h3>
        <p className="text-blue-700 text-sm">
          Review and edit the extracted transactions below. Fill in missing information and select which transactions to import.
        </p>
        <div className="mt-3 flex flex-wrap gap-4 text-sm">
          <span className="text-blue-700">
            <span className="font-medium">{data.transactions.length}</span> transactions extracted
          </span>
          <span className="text-green-700">
            <span className="font-medium">{validTransactionsCount}</span> ready to import
          </span>
          <span className="text-purple-700">
            <span className="font-medium">{selectedValidCount}</span> selected
          </span>
        </div>
      </div>

      {/* Bulk Actions */}
      <div className="flex flex-wrap gap-3">
        <button
          onClick={selectAllValid}
          className="px-4 py-2 bg-green-100 text-green-700 rounded-lg hover:bg-green-200 transition-colors text-sm font-medium"
        >
          Select All Valid ({validTransactionsCount})
        </button>
        <button
          onClick={deselectAll}
          className="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors text-sm font-medium"
        >
          Deselect All
        </button>
      </div>

      {/* Transactions Table */}
      <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50 border-b border-gray-200">
              <tr>
                <th className="w-12 px-4 py-3 text-left">
                  <span className="sr-only">Select</span>
                </th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">Date</th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">Description</th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">Amount</th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">Category</th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">Account</th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">Status</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200">
              {transactions.map((transaction, index) => (
                <tr key={transaction.id} className={`${transaction.is_valid ? 'bg-white' : 'bg-red-50'}`}>
                  {/* Selection Checkbox */}
                  <td className="px-4 py-3">
                    <input
                      type="checkbox"
                      checked={selectedTransactions.has(transaction.id)}
                      onChange={() => toggleTransactionSelection(transaction.id)}
                      disabled={!transaction.is_valid}
                      className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
                    />
                  </td>

                  {/* Date */}
                  <td className="px-4 py-3 text-sm text-gray-900">
                    {new Date(transaction.date).toLocaleDateString('en-IN')}
                  </td>

                  {/* Description */}
                  <td className="px-4 py-3">
                    <input
                      type="text"
                      value={transaction.enriched_description || ''}
                      onChange={(e) => updateTransaction(transaction.id, { 
                        enriched_description: e.target.value 
                      })}
                      className={`w-full px-3 py-2 text-sm border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                        transaction.validation_errors.some(e => e.field === 'description') 
                          ? 'border-red-300 bg-red-50' 
                          : 'border-gray-300'
                      }`}
                      placeholder="Enter description"
                    />
                  </td>

                  {/* Amount */}
                  <td className="px-4 py-3 text-sm">
                    <span className={`font-medium ${
                      transaction.type === 'credit' ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {transaction.type === 'credit' ? '+' : '-'}
                      {formatCurrency(transaction.withdrawalAmount || transaction.depositAmount)}
                    </span>
                  </td>

                  {/* Category */}
                  <td className="px-4 py-3">
                    <select
                      value={transaction.enriched_category_id || ''}
                      onChange={(e) => updateTransaction(transaction.id, { 
                        enriched_category_id: e.target.value || undefined 
                      })}
                      className={`w-full px-3 py-2 text-sm border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                        transaction.validation_errors.some(e => e.field === 'category') 
                          ? 'border-red-300 bg-red-50' 
                          : 'border-gray-300'
                      }`}
                    >
                      <option value="">Select category</option>
                      {categories
                        .filter(cat => cat.type === (transaction.type === 'credit' ? 'income' : 'expense'))
                        .map(category => (
                          <option key={category.id} value={category.id}>
                            {category.name}
                          </option>
                        ))}
                    </select>
                  </td>

                  {/* Account */}
                  <td className="px-4 py-3">
                    <select
                      value={transaction.enriched_account_id || ''}
                      onChange={(e) => updateTransaction(transaction.id, { 
                        enriched_account_id: e.target.value || undefined 
                      })}
                      className={`w-full px-3 py-2 text-sm border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                        transaction.validation_errors.some(e => e.field === 'account') 
                          ? 'border-red-300 bg-red-50' 
                          : 'border-gray-300'
                      }`}
                    >
                      <option value="">Select account</option>
                      {accounts.map(account => (
                        <option key={account.id} value={account.id}>
                          {account.name}
                        </option>
                      ))}
                    </select>
                  </td>

                  {/* Status */}
                  <td className="px-4 py-3">
                    {transaction.is_valid ? (
                      <div className="flex items-center text-green-600">
                        <Check className="w-4 h-4 mr-1" />
                        <span className="text-xs font-medium">Valid</span>
                      </div>
                    ) : (
                      <div className="flex items-center text-red-600">
                        <AlertCircle className="w-4 h-4 mr-1" />
                        <span className="text-xs font-medium">
                          {transaction.validation_errors.length} error(s)
                        </span>
                      </div>
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Error Summary */}
      {transactions.some(t => !t.is_valid) && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <h4 className="text-red-800 font-medium mb-2">Validation Issues</h4>
          <div className="text-sm text-red-700 space-y-1">
            {transactions
              .filter(t => !t.is_valid)
              .map(transaction => (
                <div key={transaction.id}>
                  <strong>Row {transactions.indexOf(transaction) + 1}:</strong>{' '}
                  {transaction.validation_errors.map(e => e.message).join(', ')}
                </div>
              ))}
          </div>
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex justify-between pt-4 border-t border-gray-200">
        <button
          onClick={onCancel}
          className="px-6 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors font-medium"
        >
          Cancel
        </button>
        
        <div className="flex gap-3">
          <div className="text-sm text-gray-600 flex items-center">
            {selectedValidCount} of {validTransactionsCount} transactions selected
          </div>
          <button
            onClick={handleSubmit}
            disabled={isSubmitting || selectedValidCount === 0}
            className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors font-medium"
          >
            {isSubmitting ? 'Importing...' : `Import ${selectedValidCount} Transactions`}
          </button>
        </div>
      </div>
    </div>
  );
}