'use client';

import React, { useState, useCallback, useRef } from 'react';
import { BankStatementData, StatementExporter } from '@repo/shared';
import PDFTransactionPreview from './PDFTransactionPreview';

interface PDFUploadProps {
  onTransactionsExtracted?: (data: BankStatementData) => void;
  className?: string;
}

interface UploadState {
  isUploading: boolean;
  progress: number;
  error: string | null;
  data: BankStatementData | null;
  showPreview: boolean;
}

export default function PDFUpload({ onTransactionsExtracted, className = '' }: PDFUploadProps) {
  const [state, setState] = useState<UploadState>({
    isUploading: false,
    progress: 0,
    error: null,
    data: null,
    showPreview: false
  });
  
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  const handleFileSelect = useCallback(async (file: File) => {
    if (!file) return;
    
    setState(prev => ({
      ...prev,
      isUploading: true,
      progress: 0,
      error: null,
      data: null
    }));
    
    try {
      const formData = new FormData();
      formData.append('file', file);
      
      setState(prev => ({ ...prev, progress: 30 }));
      
      const response = await fetch('/api/pdf/parse', {
        method: 'POST',
        body: formData
      });
      
      setState(prev => ({ ...prev, progress: 70 }));
      
      const result = await response.json();
      
      if (!response.ok) {
        throw new Error(result.error || 'Failed to parse PDF');
      }
      
      setState(prev => ({ ...prev, progress: 100 }));
      
      setTimeout(() => {
        setState(prev => ({
          ...prev,
          isUploading: false,
          data: result.data,
          showPreview: false // Start with summary view
        }));
      }, 500);
      
    } catch (error) {
      setState(prev => ({
        ...prev,
        isUploading: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }));
    }
  }, [onTransactionsExtracted]);
  
  const handleDrop = useCallback((event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    const files = Array.from(event.dataTransfer.files);
    const pdfFile = files.find(file => file.type === 'application/pdf');
    
    if (pdfFile) {
      handleFileSelect(pdfFile);
    } else {
      setState(prev => ({
        ...prev,
        error: 'Please upload a PDF file'
      }));
    }
  }, [handleFileSelect]);
  
  const handleDragOver = useCallback((event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
  }, []);
  
  const handleFileInputChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      handleFileSelect(file);
    }
  }, [handleFileSelect]);
  
  const handleDownloadText = () => {
    if (state.data) {
      StatementExporter.downloadTextFile(state.data);
    }
  };
  
  const handleDownloadCSV = () => {
    if (state.data) {
      StatementExporter.downloadCSVFile(state.data.transactions);
    }
  };
  
  const handleDownloadExcel = () => {
    if (state.data) {
      StatementExporter.downloadExcelFile(state.data);
    }
  };
  
  const resetUpload = () => {
    setState({
      isUploading: false,
      progress: 0,
      error: null,
      data: null,
      showPreview: false
    });
    
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleShowPreview = () => {
    setState(prev => ({ ...prev, showPreview: true }));
  };

  const handlePreviewCancel = () => {
    setState(prev => ({ ...prev, showPreview: false }));
  };

  const handleImportComplete = () => {
    if (onTransactionsExtracted && state.data) {
      onTransactionsExtracted(state.data);
    }
    resetUpload();
  };
  
  return (
    <div className={`space-y-6 ${className}`}>
      {/* Upload Area */}
      <div
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        className={`
          border-2 border-dashed rounded-lg p-8 text-center transition-colors
          ${state.isUploading ? 'border-blue-300 bg-blue-50' : 'border-gray-300 hover:border-gray-400'}
          ${state.error ? 'border-red-300 bg-red-50' : ''}
        `}
      >
        <input
          ref={fileInputRef}
          type="file"
          accept=".pdf"
          onChange={handleFileInputChange}
          className="hidden"
        />
        
        {state.isUploading ? (
          <div className="space-y-4">
            <div className="text-lg font-medium text-blue-600">
              Processing PDF...
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${state.progress}%` }}
              />
            </div>
            <div className="text-sm text-gray-600">
              {state.progress}% Complete
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            <div className="text-6xl text-gray-400">📄</div>
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Upload Bank Statement PDF
              </h3>
              <p className="text-gray-600 mb-4">
                Drag and drop your HDFC Bank statement PDF here, or click to browse
              </p>
              <button
                onClick={() => fileInputRef.current?.click()}
                className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
              >
                Choose PDF File
              </button>
            </div>
          </div>
        )}
      </div>
      
      {/* Error Display */}
      {state.error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center">
            <div className="text-red-400 mr-3">⚠️</div>
            <div>
              <h4 className="text-red-800 font-medium">Upload Error</h4>
              <p className="text-red-600">{state.error}</p>
            </div>
          </div>
          <button
            onClick={resetUpload}
            className="mt-3 text-red-600 hover:text-red-700 underline"
          >
            Try Again
          </button>
        </div>
      )}
      
      {/* Success and Results */}
      {state.data && !state.showPreview && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-6">
          <div className="flex items-center mb-4">
            <div className="text-green-400 mr-3 text-2xl">✅</div>
            <div>
              <h4 className="text-green-800 font-medium text-lg">
                PDF Processed Successfully!
              </h4>
              <p className="text-green-600">
                Extracted {state.data.transactions.length} transactions
              </p>
            </div>
          </div>
          
          {/* Statement Summary */}
          <div className="bg-white rounded-lg p-4 mb-4">
            <h5 className="font-medium text-gray-900 mb-3">Statement Summary</h5>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div>
                <div className="text-gray-600">Account Holder</div>
                <div className="font-medium">{state.data.accountHolder}</div>
              </div>
              <div>
                <div className="text-gray-600">Account Number</div>
                <div className="font-medium">{state.data.accountNumber}</div>
              </div>
              <div>
                <div className="text-gray-600">Period</div>
                <div className="font-medium">
                  {state.data.statementPeriod.from} to {state.data.statementPeriod.to}
                </div>
              </div>
              <div>
                <div className="text-gray-600">Transactions</div>
                <div className="font-medium">{state.data.transactions.length}</div>
              </div>
            </div>
          </div>
          
          {/* Action Buttons */}
          <div className="space-y-4">
            <div className="flex flex-wrap gap-3">
              <button
                onClick={handleShowPreview}
                className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors font-medium"
              >
                📊 Import Transactions
              </button>
            </div>
            
            {/* Download Options */}
            <div className="space-y-3">
              <h5 className="font-medium text-gray-900">Or Download Raw Data</h5>
              <div className="flex flex-wrap gap-3">
                <button
                  onClick={handleDownloadText}
                  className="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors text-sm"
                >
                  📄 Download Text
                </button>
                <button
                  onClick={handleDownloadCSV}
                  className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors text-sm"
                >
                  📊 Download CSV
                </button>
                <button
                  onClick={handleDownloadExcel}
                  className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors text-sm"
                >
                  📈 Download Excel
                </button>
              </div>
            </div>
          </div>
          
          {/* Reset Button */}
          <button
            onClick={resetUpload}
            className="mt-4 text-gray-600 hover:text-gray-700 underline text-sm"
          >
            Upload Another Statement
          </button>
        </div>
      )}

      {/* Transaction Preview */}
      {state.data && state.showPreview && (
        <PDFTransactionPreview
          data={state.data}
          onImportComplete={handleImportComplete}
          onCancel={handlePreviewCancel}
        />
      )}
    </div>
  );
}
