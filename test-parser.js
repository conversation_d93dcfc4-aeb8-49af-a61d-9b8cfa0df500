const fs = require('fs');

// Sample HDFC bank statement text (similar to what the PDF parser would extract)
const sampleHDFCText = `
HDFC BANK Ltd.                                     Page No .:   1                                        Statement of accounts

MR      ARAVINTH E                                                                        
2/95 WEST STREET                                                         
AKKIYAMPATTI                                                             
NAMAKKAL 637409                                                          
TAMIL NADU                                                               
SENDAMANGALAM                                                            

JOINT HOLDERS :                                                          
                                                                         
Nomination : Not Registered                                              
Statement From      : 01/04/2025  To: 27/06/2025                         

Account Branch : ANNA NAGAR I
Address        : AG 21/23, 4TH AVENUE,
                 SHANTHI COLONY,
                 ANNA NAGAR,
City           : CHENNAI600040
State          : TAMIL NADU
Phone no.      : ********/********
Email          : <EMAIL>
OD Limit       : 0  Currency : INR
Cust ID        : *********
Account No     : **************   PRIME POTENTIAL
A/C Open Date  : 12/01/2019
Account Status : Regular  
RTGS/NEFT IFSC : HDFC0000017    MICR : *********   
Branch Code    : 17       
Account Type   : INSTANT SAVING SALARY PREMIUM (161)

Date      Narration                            Chq./Ref.No.      Value Dt  Withdrawal Amt.        Deposit Amt.     Closing Balance

01/04/25  NEFT DR-ICIC0001550-ARAVINTH E-NETBANK,   HDFCN52025040150264811  01/04/25          78,100.00                             188,702.88   
          MUM-HDFCN52025040150264811-MONTHLY AND C                                                                                          
          C                                                                                                                                 
02/04/25  UPI-ARAVINTH E-ARAVINTH-ELANGOVAN@DBS-DB  ****************  02/04/25          11,000.00                             177,702.88   
          SS0IN0460-************-RD                                                                                                         
02/04/25  ACH C- MUTHOOT FINANCE LIMI-7GV219790     ****************  02/04/25                                 13.00          177,715.88   
02/04/25  ACH C- MUTHOOT FINANCE LIMI-0000000007HZ  ****************  02/04/25                                  7.00          177,722.88   
02/04/25  ACH C- MUTHOOT FINANCE LIMI-0000000007HM  0000006665977283  02/04/25                                 71.00          177,793.88   
03/04/25  UPI-INDIAN CLEARING CORP-ICCLZERODHA@YES  0000************  03/04/25          10,000.00                             167,793.88   
          PAY-HDFC0000060-************-MERCHANT UP                                                                                          
          I TXN                                                                                                                             
03/04/25  UPI-INDIAN CLEARING CORP-ICCLZERODHA@YES  0000************  03/04/25             500.00                             167,293.88   
          PAY-HDFC0000060-************-MERCHANT UP                                                                                          
          I TXN                                                                                                                             
03/04/25  UPI-INDIAN CLEARING CORP-ICCLZERODHA@YES  0000************  03/04/25             500.00                             166,793.88   
          PAY-HDFC0000060-************-MERCHANT UP                                                                                          
          I TXN                                                                                                                             
03/04/25  UPI-INDIAN CLEARING CORP-ICCLZERODHA@YES  0000************  03/04/25             500.00                             166,293.88   
          PAY-HDFC0000060-************-MERCHANT UP                                                                                          
          I TXN                                                                                                                             
03/04/25  UPI-INDIAN CLEARING CORP-ICCLZERODHA@YES  0000************  03/04/25           1,500.00                             164,793.88   
          PAY-HDFC0000060-************-MERCHANT UP                                                                                          
          I TXN                                                                                                                             

HDFC BANK LIMITED
*Closing balance includes funds earmarked for hold and uncleared funds
Contents of this statement will be considered correct if no error is reported within 30 days of receipt of statement.
`;

// Simulate what PDF parser would extract (concatenated format)
const concatenatedText = `
HDFC BANK Ltd. Page No .: 1 Statement of accounts
MR ARAVINTH E
2/95 WEST STREET
AKKIYAMPATTI
NAMAKKAL 637409
TAMIL NADU
SENDAMANGALAM
JOINT HOLDERS :
Nomination : Not Registered
Statement From : 01/04/2025 To: 27/06/2025
Account Branch : ANNA NAGAR I
Address : AG 21/23, 4TH AVENUE, SHANTHI COLONY, ANNA NAGAR,
City : CHENNAI600040
State : TAMIL NADU
Phone no. : ********/********
Email : <EMAIL>
OD Limit : 0 Currency : INR
Cust ID : *********
Account No : ************** PRIME POTENTIAL
A/C Open Date : 12/01/2019
Account Status : Regular
RTGS/NEFT IFSC : HDFC0000017 MICR : *********
Branch Code : 17
Account Type : INSTANT SAVING SALARY PREMIUM (161)
DateNarrationChq./Ref.No.Value DtWithdrawal Amt.Deposit Amt.Closing Balance
01/04/25NEFT DR-ICIC0001550-ARAVINTH E-NETBANK,HDFCN5202504015026481101/04/2578,100.00188,702.88
MUM-HDFCN52025040150264811-MONTHLY AND
C
C
02/04/25UPI-ARAVINTH
E-ARAVINTH-ELANGOVAN@DBS-DB
****************02/04/2511,000.00177,702.88
SS0IN0460-************-RD
02/04/25ACH C- MUTHOOT FINANCE LIMI-7GV219790****************02/04/2513.00177,715.88
02/04/25ACH C- MUTHOOT FINANCE LIMI-0000000007HZ****************02/04/257.00177,722.88
02/04/25ACH C- MUTHOOT FINANCE LIMI-0000000007HM000000666597728302/04/2571.00177,793.88
03/04/25UPI-INDIAN CLEARING
CORP-ICCLZERODHA@YES
0000************03/04/2510,000.00167,793.88
PAY-HDFC0000060-************-MERCHANT UP
I TXN
03/04/25UPI-INDIAN CLEARING
CORP-ICCLZERODHA@YES
0000************03/04/25500.00167,293.88
PAY-HDFC0000060-************-MERCHANT UP
I TXN
03/04/25UPI-INDIAN CLEARING
CORP-ICCLZERODHA@YES
0000************03/04/25500.00166,793.88
PAY-HDFC0000060-************-MERCHANT UP
I TXN
03/04/25UPI-INDIAN CLEARING
CORP-ICCLZERODHA@YES
0000************03/04/25500.00166,293.88
PAY-HDFC0000060-************-MERCHANT UP
I TXN
03/04/25UPI-INDIAN CLEARING
CORP-ICCLZERODHA@YES
0000************03/04/251,500.00164,793.88
PAY-HDFC0000060-************-MERCHANT UP
I TXN
HDFC BANK LIMITED
`;

// Test the parsing logic with our sample data
console.log('Testing HDFC Bank Statement Parser with sample data...\n');

// Expected results based on the PDF
const expectedResults = [
  { date: '01/04/25', amount: 78100.00, type: 'debit', balance: 188702.88 },
  { date: '02/04/25', amount: 11000.00, type: 'debit', balance: 177702.88 },
  { date: '02/04/25', amount: 13.00, type: 'credit', balance: 177715.88 },
  { date: '02/04/25', amount: 7.00, type: 'credit', balance: 177722.88 },
  { date: '02/04/25', amount: 71.00, type: 'credit', balance: 177793.88 },
  { date: '03/04/25', amount: 10000.00, type: 'debit', balance: 167793.88 },
  { date: '03/04/25', amount: 500.00, type: 'debit', balance: 167293.88 },
  { date: '03/04/25', amount: 500.00, type: 'debit', balance: 166793.88 },
  { date: '03/04/25', amount: 500.00, type: 'debit', balance: 166293.88 },
  { date: '03/04/25', amount: 1500.00, type: 'debit', balance: 164793.88 }
];

console.log('Expected Results:');
expectedResults.forEach((expected, index) => {
  console.log(`${index + 1}. ${expected.date} - ${expected.type} - ₹${expected.amount.toLocaleString()} - Balance: ₹${expected.balance.toLocaleString()}`);
});

console.log('\n=== Testing Parser Logic ===');

// Test individual transaction parsing (using the actual problematic concatenated format)
const testLines = [
  "01/04/25NEFT DR-ICIC0001550-ARAVINTH E-NETBANK,HDFCN5202504015026481101/04/2578,100.00188,702.88",
  "02/04/25ACH C- MUTHOOT FINANCE LIMI-7GV219790****************02/04/2513.00177,715.88",
  "03/04/25UPI-INDIAN CLEARING CORP-ICCLZERODHA@YES 0000************03/04/2510,000.00167,793.88"
];

// Note: The issue is that PDF-to-text conversion produces concatenated amounts
// Our parser needs to extract these correctly.

// Simple test of our parsing logic
function testParseHDFCTransactionLine(fullLine, transactionDate) {
  console.log('\\nTesting line:', fullLine);
  
  try {
    // Remove the initial transaction date
    let remaining = fullLine.replace(/^\d{2}\/\d{2}\/\d{2,4}/, '').trim();
    console.log('After removing initial date:', remaining);
    
    // Step 1: Extract closing balance (rightmost amount)
    const closingBalanceMatch = remaining.match(/(\d{1,3}(?:,\d{3})*\.\d{2})$/);
    if (!closingBalanceMatch) {
      console.log('Could not find closing balance');
      return null;
    }
    const closingBalance = parseFloat(closingBalanceMatch[1].replace(/,/g, ''));
    console.log('Found closing balance:', closingBalanceMatch[1], '->', closingBalance);
    
    // Remove closing balance from string
    remaining = remaining.substring(0, remaining.lastIndexOf(closingBalanceMatch[1])).trim();
    console.log('After removing closing balance:', remaining);
    
    // Step 2: Extract transaction amount (next rightmost amount)
    const transactionAmountMatch = remaining.match(/(\d{1,3}(?:,\d{3})*\.\d{2})$/);
    if (!transactionAmountMatch) {
      console.log('Could not find transaction amount');
      return null;
    }
    const transactionAmount = parseFloat(transactionAmountMatch[1].replace(/,/g, ''));
    console.log('Found transaction amount:', transactionAmountMatch[1], '->', transactionAmount);
    
    // Remove transaction amount from string
    remaining = remaining.substring(0, remaining.lastIndexOf(transactionAmountMatch[1])).trim();
    console.log('After removing transaction amount:', remaining);
    
    // Step 3: Extract value date (rightmost date pattern)
    const valueDateMatch = remaining.match(/(\d{2}\/\d{2}\/\d{2,4})$/);
    let valueDate = transactionDate; // Default to transaction date
    if (valueDateMatch) {
      valueDate = valueDateMatch[1];
      remaining = remaining.substring(0, remaining.lastIndexOf(valueDateMatch[1])).trim();
      console.log('Found value date:', valueDate);
    }
    console.log('After removing value date:', remaining);
    
    // Step 4: Extract reference number (longest sequence of digits)
    const refMatches = remaining.match(/\d{10,}/g);
    let refNo = '';
    if (refMatches) {
      // Take the longest sequence as reference number
      refNo = refMatches.reduce((longest, current) => 
        current.length > longest.length ? current : longest
      );
      remaining = remaining.replace(refNo, '').trim();
      console.log('Found reference number:', refNo);
    }
    console.log('After removing reference number:', remaining);
    
    // Step 5: Clean up narration (what's left)
    const narration = remaining
      .replace(/\s+/g, ' ')
      .replace(/^[-,\s]+|[-,\s]+$/g, '')
      .trim();
    
    console.log('Final parsed fields:', {
      transactionAmount,
      closingBalance,
      narration,
      refNo,
      valueDate
    });
    
    return {
      transactionAmount,
      closingBalance,
      narration,
      refNo,
      valueDate
    };
  } catch (error) {
    console.error('Error parsing HDFC transaction line:', error);
    return null;
  }
}

// Test each line
testLines.forEach((line, index) => {
  const dateMatch = line.match(/^(\d{2}\/\d{2}\/\d{2,4})/);
  if (dateMatch) {
    console.log('\\n=== Testing Transaction', index + 1, '===');
    const result = testParseHDFCTransactionLine(line, dateMatch[1]);
    if (result) {
      const expected = expectedResults[index === 0 ? 0 : index === 1 ? 2 : 5]; // Map to expected results
      console.log('\\nComparison with expected:');
      console.log('Expected amount:', expected.amount, 'vs Parsed:', result.transactionAmount, result.transactionAmount === expected.amount ? '✅' : '❌');
      console.log('Expected balance:', expected.balance, 'vs Parsed:', result.closingBalance, result.closingBalance === expected.balance ? '✅' : '❌');
    }
  }
});

console.log('\\n=== Test Complete ===');
console.log('If you see ✅ marks, the parsing is working correctly!');
console.log('If you see ❌ marks, there may still be issues to fix.');
